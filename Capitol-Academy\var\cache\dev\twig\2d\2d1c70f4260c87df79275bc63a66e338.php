<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/contacts/show.html.twig */
class __TwigTemplate_543e4b489dfa9f6fdc516de6d99f0c78 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/contacts/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/contacts/show.html.twig"));

        // line 1
        yield from $this->load("admin/contacts/show.html.twig", 1, "266464356")->unwrap()->yield(CoreExtension::merge($context, ["entity_name" => "Contact", "entity_title" => CoreExtension::getAttribute($this->env, $this->source,         // line 3
(isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 3, $this->source); })()), "fullName", [], "any", false, false, false, 3), "entity_code" => CoreExtension::getAttribute($this->env, $this->source,         // line 4
(isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 4, $this->source); })()), "fullName", [], "any", false, false, false, 4), "entity_icon" => "fas fa-envelope", "breadcrumb_items" => [["path" => "admin_dashboard", "title" => "Home"], ["path" => "admin_contacts", "title" => "Contacts"], ["title" => CoreExtension::getAttribute($this->env, $this->source,         // line 9
(isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 9, $this->source); })()), "fullName", [], "any", false, false, false, 9), "active" => true]], "back_path" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_contacts"), "print_function" => "printContactDetails"]));
        // line 167
        yield "
";
        // line 168
        yield from $this->unwrap()->yieldBlock('javascripts', $context, $blocks);
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 169
        yield "<script>
\$(document).ready(function() {
    // Toggle processed status functionality
    \$('.toggle-processed-btn').on('click', function() {
        const button = \$(this);
        const contactSlug = button.data('contact-slug');
        const originalHtml = button.html();

        // Show loading state
        button.prop('disabled', true);
        button.html('<i class=\"fas fa-spinner fa-spin me-1\"></i>Processing...');

        // Make AJAX request
        \$.ajax({
            url: '";
        // line 183
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_contact_toggle_processed", ["slug" => "PLACEHOLDER"]);
        yield "'.replace('PLACEHOLDER', contactSlug),
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            success: function(response) {
                if (response.success) {
                    // Remove success message - just reload the page
                    setTimeout(function() {
                        location.reload();
                    }, 500);
                } else {
                    showAlert('danger', response.message || 'An error occurred');
                    button.prop('disabled', false);
                    button.html(originalHtml);
                }
            },
            error: function(xhr) {
                let errorMessage = 'An error occurred while updating the contact status.';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 403) {
                    errorMessage = 'You do not have permission to perform this action.';
                } else if (xhr.status === 404) {
                    errorMessage = 'Contact not found.';
                }

                showAlert('danger', errorMessage);
                button.prop('disabled', false);
                button.html(originalHtml);
            }
        });
    });

    // Function to show Capitol Academy styled alert messages
    function showAlert(type, message) {
        const alertStyle = type === 'success' ? 
            'background: #011a2d; color: white; border: 1px solid #011a2d;' : 
            'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;';
        
        const alertHtml = `
            <div class=\"alert alert-dismissible fade show\" role=\"alert\" style=\"\${alertStyle} border-radius: 8px; margin-bottom: 1rem;\">
                <i class=\"fas fa-\${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2\"></i>
                \${message}
                <button type=\"button\" class=\"btn-close \${type === 'success' ? 'btn-close-white' : ''}\" data-bs-dismiss=\"alert\" aria-label=\"Close\"></button>
            </div>
        `;

        // Remove existing alerts to prevent duplicates
        \$('.alert').remove();

        // Add new alert above the content
        \$('.container-fluid').prepend(alertHtml);

        // Auto-dismiss after 2 seconds
        setTimeout(function() {
            \$('.alert').fadeOut();
        }, 2000);
    }

    // Print functionality enhancement
    window.addEventListener('beforeprint', function() {
        // Hide action buttons when printing
        \$('.action-buttons').hide();
    });

    window.addEventListener('afterprint', function() {
        // Show action buttons after printing
        \$('.action-buttons').show();
    });
});

// Print function for the preview layout
function printContactDetails() {
    window.print();
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/contacts/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  96 => 183,  80 => 169,  57 => 168,  54 => 167,  52 => 9,  51 => 4,  50 => 3,  49 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Contact',
    'entity_title': contact.fullName,
    'entity_code': contact.fullName,
    'entity_icon': 'fas fa-envelope',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_contacts', 'title': 'Contacts'},
        {'title': contact.fullName, 'active': true}
    ],
    'back_path': path('admin_contacts'),
    'print_function': 'printContactDetails'
} %}

{% block preview_content %}
    <!-- Contact Information Row -->
    <div class=\"row\">
        <!-- Full Name -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-user text-primary mr-1\"></i>
                    Full Name
                </label>
                <div class=\"enhanced-display-field\">
                    {{ contact.fullName }}
                </div>
            </div>
        </div>

        <!-- Email Address -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-envelope text-primary mr-1\"></i>
                    Email Address
                </label>
                <div class=\"enhanced-display-field\">
                    <a href=\"mailto:{{ contact.email }}\" class=\"text-decoration-none\" style=\"color: #011a2d;\">{{ contact.email }}</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Subject and Country Row -->
    <div class=\"row\">
        <!-- Subject -->
        <div class=\"col-md-8\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-tag text-primary mr-1\"></i>
                    Subject
                </label>
                <div class=\"enhanced-display-field\">
                    {{ contact.subject ?? 'No subject provided' }}
                </div>
            </div>
        </div>

        <!-- Country -->
        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-globe text-primary mr-1\"></i>
                    Country
                </label>
                <div class=\"enhanced-display-field\">
                    {{ contact.country ?? 'Not specified' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Message -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-comment text-primary mr-1\"></i>
            Message
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 120px;\">
            {{ contact.message ? contact.message|nl2br : 'No message provided' }}
        </div>
    </div>

    <!-- Contact Details Row -->
    <div class=\"row\">
        <!-- Created Date -->
        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Contact Date
                </label>
                <div class=\"enhanced-display-field\">
                    {{ contact.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                </div>
            </div>
        </div>

        <!-- Status -->
        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                    Status
                </label>
                <div class=\"enhanced-display-field\">
                    {% if contact.processed %}
                        <span class=\"badge bg-success\">Processed</span>
                    {% else %}
                        <span class=\"badge bg-warning\">Pending</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Source Page -->
        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-link text-primary mr-1\"></i>
                    Source Page
                </label>
                <div class=\"enhanced-display-field\">
                    {{ contact.sourcePage ?? 'Unknown' }}
                </div>
            </div>
        </div>
    </div>

    <!-- IP Address -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-map-marker-alt text-primary mr-1\"></i>
            IP Address
        </label>
        <div class=\"enhanced-display-field\">
            {{ contact.ipAddress ?? 'Not recorded' }}
        </div>
    </div>

    <!-- Action Buttons -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-cogs text-primary mr-1\"></i>
            Actions
        </label>
        <div class=\"enhanced-display-field\">
            <div class=\"action-buttons d-flex gap-2\">
                <button type=\"button\" 
                        class=\"btn btn-sm toggle-processed-btn\" 
                        data-contact-slug=\"{{ contact.urlSlug }}\"
                        style=\"background: {% if contact.processed %}#ffc107{% else %}#28a745{% endif %}; color: white; border-radius: 6px; padding: 0.4rem 0.8rem;\">
                    <i class=\"fas {% if contact.processed %}fa-undo{% else %}fa-check{% endif %} me-1\"></i>
                    {% if contact.processed %}Mark as Unprocessed{% else %}Mark as Processed{% endif %}
                </button>
                <a href=\"mailto:{{ contact.email }}\" 
                   class=\"btn btn-sm\" 
                   style=\"background: #011a2d; color: white; border-radius: 6px; padding: 0.4rem 0.8rem;\">
                    <i class=\"fas fa-reply me-1\"></i>Reply via Email
                </a>
            </div>
        </div>
    </div>
{% endblock %}
{% endembed %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Toggle processed status functionality
    \$('.toggle-processed-btn').on('click', function() {
        const button = \$(this);
        const contactSlug = button.data('contact-slug');
        const originalHtml = button.html();

        // Show loading state
        button.prop('disabled', true);
        button.html('<i class=\"fas fa-spinner fa-spin me-1\"></i>Processing...');

        // Make AJAX request
        \$.ajax({
            url: '{{ path('admin_contact_toggle_processed', {'slug': 'PLACEHOLDER'}) }}'.replace('PLACEHOLDER', contactSlug),
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            success: function(response) {
                if (response.success) {
                    // Remove success message - just reload the page
                    setTimeout(function() {
                        location.reload();
                    }, 500);
                } else {
                    showAlert('danger', response.message || 'An error occurred');
                    button.prop('disabled', false);
                    button.html(originalHtml);
                }
            },
            error: function(xhr) {
                let errorMessage = 'An error occurred while updating the contact status.';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 403) {
                    errorMessage = 'You do not have permission to perform this action.';
                } else if (xhr.status === 404) {
                    errorMessage = 'Contact not found.';
                }

                showAlert('danger', errorMessage);
                button.prop('disabled', false);
                button.html(originalHtml);
            }
        });
    });

    // Function to show Capitol Academy styled alert messages
    function showAlert(type, message) {
        const alertStyle = type === 'success' ? 
            'background: #011a2d; color: white; border: 1px solid #011a2d;' : 
            'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;';
        
        const alertHtml = `
            <div class=\"alert alert-dismissible fade show\" role=\"alert\" style=\"\${alertStyle} border-radius: 8px; margin-bottom: 1rem;\">
                <i class=\"fas fa-\${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2\"></i>
                \${message}
                <button type=\"button\" class=\"btn-close \${type === 'success' ? 'btn-close-white' : ''}\" data-bs-dismiss=\"alert\" aria-label=\"Close\"></button>
            </div>
        `;

        // Remove existing alerts to prevent duplicates
        \$('.alert').remove();

        // Add new alert above the content
        \$('.container-fluid').prepend(alertHtml);

        // Auto-dismiss after 2 seconds
        setTimeout(function() {
            \$('.alert').fadeOut();
        }, 2000);
    }

    // Print functionality enhancement
    window.addEventListener('beforeprint', function() {
        // Hide action buttons when printing
        \$('.action-buttons').hide();
    });

    window.addEventListener('afterprint', function() {
        // Show action buttons after printing
        \$('.action-buttons').show();
    });
});

// Print function for the preview layout
function printContactDetails() {
    window.print();
}
</script>
{% endblock %}
", "admin/contacts/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\contacts\\show.html.twig");
    }
}


/* admin/contacts/show.html.twig */
class __TwigTemplate_543e4b489dfa9f6fdc516de6d99f0c78___266464356 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'preview_content' => [$this, 'block_preview_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "components/admin_preview_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/contacts/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/contacts/show.html.twig"));

        $this->parent = $this->load("components/admin_preview_layout.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 15
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_preview_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        // line 16
        yield "    <!-- Contact Information Row -->
    <div class=\"row\">
        <!-- Full Name -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-user text-primary mr-1\"></i>
                    Full Name
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 26
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 26, $this->source); })()), "fullName", [], "any", false, false, false, 26), "html", null, true);
        yield "
                </div>
            </div>
        </div>

        <!-- Email Address -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-envelope text-primary mr-1\"></i>
                    Email Address
                </label>
                <div class=\"enhanced-display-field\">
                    <a href=\"mailto:";
        // line 39
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 39, $this->source); })()), "email", [], "any", false, false, false, 39), "html", null, true);
        yield "\" class=\"text-decoration-none\" style=\"color: #011a2d;\">";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 39, $this->source); })()), "email", [], "any", false, false, false, 39), "html", null, true);
        yield "</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Subject and Country Row -->
    <div class=\"row\">
        <!-- Subject -->
        <div class=\"col-md-8\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-tag text-primary mr-1\"></i>
                    Subject
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 55
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["contact"] ?? null), "subject", [], "any", true, true, false, 55) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 55, $this->source); })()), "subject", [], "any", false, false, false, 55)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 55, $this->source); })()), "subject", [], "any", false, false, false, 55), "html", null, true)) : ("No subject provided"));
        yield "
                </div>
            </div>
        </div>

        <!-- Country -->
        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-globe text-primary mr-1\"></i>
                    Country
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 68
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["contact"] ?? null), "country", [], "any", true, true, false, 68) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 68, $this->source); })()), "country", [], "any", false, false, false, 68)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 68, $this->source); })()), "country", [], "any", false, false, false, 68), "html", null, true)) : ("Not specified"));
        yield "
                </div>
            </div>
        </div>
    </div>

    <!-- Message -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-comment text-primary mr-1\"></i>
            Message
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 120px;\">
            ";
        // line 81
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 81, $this->source); })()), "message", [], "any", false, false, false, 81)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? (Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 81, $this->source); })()), "message", [], "any", false, false, false, 81), "html", null, true))) : ("No message provided"));
        yield "
        </div>
    </div>

    <!-- Contact Details Row -->
    <div class=\"row\">
        <!-- Created Date -->
        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Contact Date
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 95
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 95, $this->source); })()), "createdAt", [], "any", false, false, false, 95), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "
                </div>
            </div>
        </div>

        <!-- Status -->
        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                    Status
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 108
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 108, $this->source); })()), "processed", [], "any", false, false, false, 108)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 109
            yield "                        <span class=\"badge bg-success\">Processed</span>
                    ";
        } else {
            // line 111
            yield "                        <span class=\"badge bg-warning\">Pending</span>
                    ";
        }
        // line 113
        yield "                </div>
            </div>
        </div>

        <!-- Source Page -->
        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-link text-primary mr-1\"></i>
                    Source Page
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 125
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["contact"] ?? null), "sourcePage", [], "any", true, true, false, 125) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 125, $this->source); })()), "sourcePage", [], "any", false, false, false, 125)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 125, $this->source); })()), "sourcePage", [], "any", false, false, false, 125), "html", null, true)) : ("Unknown"));
        yield "
                </div>
            </div>
        </div>
    </div>

    <!-- IP Address -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-map-marker-alt text-primary mr-1\"></i>
            IP Address
        </label>
        <div class=\"enhanced-display-field\">
            ";
        // line 138
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["contact"] ?? null), "ipAddress", [], "any", true, true, false, 138) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 138, $this->source); })()), "ipAddress", [], "any", false, false, false, 138)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 138, $this->source); })()), "ipAddress", [], "any", false, false, false, 138), "html", null, true)) : ("Not recorded"));
        yield "
        </div>
    </div>

    <!-- Action Buttons -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-cogs text-primary mr-1\"></i>
            Actions
        </label>
        <div class=\"enhanced-display-field\">
            <div class=\"action-buttons d-flex gap-2\">
                <button type=\"button\" 
                        class=\"btn btn-sm toggle-processed-btn\" 
                        data-contact-slug=\"";
        // line 152
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 152, $this->source); })()), "urlSlug", [], "any", false, false, false, 152), "html", null, true);
        yield "\"
                        style=\"background: ";
        // line 153
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 153, $this->source); })()), "processed", [], "any", false, false, false, 153)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield "#ffc107";
        } else {
            yield "#28a745";
        }
        yield "; color: white; border-radius: 6px; padding: 0.4rem 0.8rem;\">
                    <i class=\"fas ";
        // line 154
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 154, $this->source); })()), "processed", [], "any", false, false, false, 154)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield "fa-undo";
        } else {
            yield "fa-check";
        }
        yield " me-1\"></i>
                    ";
        // line 155
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 155, $this->source); })()), "processed", [], "any", false, false, false, 155)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            yield "Mark as Unprocessed";
        } else {
            yield "Mark as Processed";
        }
        // line 156
        yield "                </button>
                <a href=\"mailto:";
        // line 157
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["contact"]) || array_key_exists("contact", $context) ? $context["contact"] : (function () { throw new RuntimeError('Variable "contact" does not exist.', 157, $this->source); })()), "email", [], "any", false, false, false, 157), "html", null, true);
        yield "\" 
                   class=\"btn btn-sm\" 
                   style=\"background: #011a2d; color: white; border-radius: 6px; padding: 0.4rem 0.8rem;\">
                    <i class=\"fas fa-reply me-1\"></i>Reply via Email
                </a>
            </div>
        </div>
    </div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/contacts/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  738 => 157,  735 => 156,  729 => 155,  721 => 154,  713 => 153,  709 => 152,  692 => 138,  676 => 125,  662 => 113,  658 => 111,  654 => 109,  652 => 108,  636 => 95,  619 => 81,  603 => 68,  587 => 55,  566 => 39,  550 => 26,  538 => 16,  525 => 15,  502 => 1,  96 => 183,  80 => 169,  57 => 168,  54 => 167,  52 => 9,  51 => 4,  50 => 3,  49 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Contact',
    'entity_title': contact.fullName,
    'entity_code': contact.fullName,
    'entity_icon': 'fas fa-envelope',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_contacts', 'title': 'Contacts'},
        {'title': contact.fullName, 'active': true}
    ],
    'back_path': path('admin_contacts'),
    'print_function': 'printContactDetails'
} %}

{% block preview_content %}
    <!-- Contact Information Row -->
    <div class=\"row\">
        <!-- Full Name -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-user text-primary mr-1\"></i>
                    Full Name
                </label>
                <div class=\"enhanced-display-field\">
                    {{ contact.fullName }}
                </div>
            </div>
        </div>

        <!-- Email Address -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-envelope text-primary mr-1\"></i>
                    Email Address
                </label>
                <div class=\"enhanced-display-field\">
                    <a href=\"mailto:{{ contact.email }}\" class=\"text-decoration-none\" style=\"color: #011a2d;\">{{ contact.email }}</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Subject and Country Row -->
    <div class=\"row\">
        <!-- Subject -->
        <div class=\"col-md-8\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-tag text-primary mr-1\"></i>
                    Subject
                </label>
                <div class=\"enhanced-display-field\">
                    {{ contact.subject ?? 'No subject provided' }}
                </div>
            </div>
        </div>

        <!-- Country -->
        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-globe text-primary mr-1\"></i>
                    Country
                </label>
                <div class=\"enhanced-display-field\">
                    {{ contact.country ?? 'Not specified' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Message -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-comment text-primary mr-1\"></i>
            Message
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 120px;\">
            {{ contact.message ? contact.message|nl2br : 'No message provided' }}
        </div>
    </div>

    <!-- Contact Details Row -->
    <div class=\"row\">
        <!-- Created Date -->
        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Contact Date
                </label>
                <div class=\"enhanced-display-field\">
                    {{ contact.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                </div>
            </div>
        </div>

        <!-- Status -->
        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                    Status
                </label>
                <div class=\"enhanced-display-field\">
                    {% if contact.processed %}
                        <span class=\"badge bg-success\">Processed</span>
                    {% else %}
                        <span class=\"badge bg-warning\">Pending</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Source Page -->
        <div class=\"col-md-4\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-link text-primary mr-1\"></i>
                    Source Page
                </label>
                <div class=\"enhanced-display-field\">
                    {{ contact.sourcePage ?? 'Unknown' }}
                </div>
            </div>
        </div>
    </div>

    <!-- IP Address -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-map-marker-alt text-primary mr-1\"></i>
            IP Address
        </label>
        <div class=\"enhanced-display-field\">
            {{ contact.ipAddress ?? 'Not recorded' }}
        </div>
    </div>

    <!-- Action Buttons -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-cogs text-primary mr-1\"></i>
            Actions
        </label>
        <div class=\"enhanced-display-field\">
            <div class=\"action-buttons d-flex gap-2\">
                <button type=\"button\" 
                        class=\"btn btn-sm toggle-processed-btn\" 
                        data-contact-slug=\"{{ contact.urlSlug }}\"
                        style=\"background: {% if contact.processed %}#ffc107{% else %}#28a745{% endif %}; color: white; border-radius: 6px; padding: 0.4rem 0.8rem;\">
                    <i class=\"fas {% if contact.processed %}fa-undo{% else %}fa-check{% endif %} me-1\"></i>
                    {% if contact.processed %}Mark as Unprocessed{% else %}Mark as Processed{% endif %}
                </button>
                <a href=\"mailto:{{ contact.email }}\" 
                   class=\"btn btn-sm\" 
                   style=\"background: #011a2d; color: white; border-radius: 6px; padding: 0.4rem 0.8rem;\">
                    <i class=\"fas fa-reply me-1\"></i>Reply via Email
                </a>
            </div>
        </div>
    </div>
{% endblock %}
{% endembed %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Toggle processed status functionality
    \$('.toggle-processed-btn').on('click', function() {
        const button = \$(this);
        const contactSlug = button.data('contact-slug');
        const originalHtml = button.html();

        // Show loading state
        button.prop('disabled', true);
        button.html('<i class=\"fas fa-spinner fa-spin me-1\"></i>Processing...');

        // Make AJAX request
        \$.ajax({
            url: '{{ path('admin_contact_toggle_processed', {'slug': 'PLACEHOLDER'}) }}'.replace('PLACEHOLDER', contactSlug),
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            success: function(response) {
                if (response.success) {
                    // Remove success message - just reload the page
                    setTimeout(function() {
                        location.reload();
                    }, 500);
                } else {
                    showAlert('danger', response.message || 'An error occurred');
                    button.prop('disabled', false);
                    button.html(originalHtml);
                }
            },
            error: function(xhr) {
                let errorMessage = 'An error occurred while updating the contact status.';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.status === 403) {
                    errorMessage = 'You do not have permission to perform this action.';
                } else if (xhr.status === 404) {
                    errorMessage = 'Contact not found.';
                }

                showAlert('danger', errorMessage);
                button.prop('disabled', false);
                button.html(originalHtml);
            }
        });
    });

    // Function to show Capitol Academy styled alert messages
    function showAlert(type, message) {
        const alertStyle = type === 'success' ? 
            'background: #011a2d; color: white; border: 1px solid #011a2d;' : 
            'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;';
        
        const alertHtml = `
            <div class=\"alert alert-dismissible fade show\" role=\"alert\" style=\"\${alertStyle} border-radius: 8px; margin-bottom: 1rem;\">
                <i class=\"fas fa-\${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2\"></i>
                \${message}
                <button type=\"button\" class=\"btn-close \${type === 'success' ? 'btn-close-white' : ''}\" data-bs-dismiss=\"alert\" aria-label=\"Close\"></button>
            </div>
        `;

        // Remove existing alerts to prevent duplicates
        \$('.alert').remove();

        // Add new alert above the content
        \$('.container-fluid').prepend(alertHtml);

        // Auto-dismiss after 2 seconds
        setTimeout(function() {
            \$('.alert').fadeOut();
        }, 2000);
    }

    // Print functionality enhancement
    window.addEventListener('beforeprint', function() {
        // Hide action buttons when printing
        \$('.action-buttons').hide();
    });

    window.addEventListener('afterprint', function() {
        // Show action buttons after printing
        \$('.action-buttons').show();
    });
});

// Print function for the preview layout
function printContactDetails() {
    window.print();
}
</script>
{% endblock %}
", "admin/contacts/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\contacts\\show.html.twig");
    }
}
