<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/market_analysis/create.html.twig */
class __TwigTemplate_9d0dbeea0caa5fe243adcd1629dd5f1e extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/create.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/create.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Create Market Analysis - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Create New Market Analysis";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_index");
        yield "\">Market Analysis</a></li>
<li class=\"breadcrumb-item active\">Create Analysis</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-chart-line mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Market Analysis
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Market Analysis Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_index");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Market Analysis
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("market_analysis_create"), "html", null, true);
        yield "\">
            <input type=\"hidden\" name=\"is_active\" value=\"1\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Title and Asset Type Row -->
                            <div class=\"row\">
                                <!-- Analysis Title -->
                                <div class=\"col-md-8\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-heading text-primary mr-1\"></i>
                                            Analysis Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               placeholder=\"Enter analysis title...\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide an analysis title.
                                        </div>
                                    </div>
                                </div>

                                <!-- Asset Type -->
                                <div class=\"col-md-4\">
                                    <div class=\"form-group\">
                                        <label for=\"asset_type\" class=\"form-label\">
                                            <i class=\"fas fa-chart-bar text-primary mr-1\"></i>
                                            Asset Type <span class=\"text-danger\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-field\"
                                                id=\"asset_type\"
                                                name=\"asset_type\"
                                                required
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; padding: 0.75rem 2.25rem 0.75rem 0.75rem;\">
                                            <option value=\"\">Select Asset Type</option>
                                            <option value=\"stocks\">Stocks</option>
                                            <option value=\"forex\">Forex</option>
                                            <option value=\"crypto\">Crypto</option>
                                            <option value=\"crude_oil\">Crude Oil</option>
                                            <option value=\"gold\">Gold</option>
                                            <option value=\"commodities\">Commodities</option>
                                        </select>
                                        <div class=\"invalid-feedback\">
                                            Please select an asset type.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Author and Date Row -->
                            <div class=\"row\">
                                <!-- Author -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"author\" class=\"form-label\">
                                            <i class=\"fas fa-user-edit text-primary mr-1\"></i>
                                            Author
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"author\"
                                               name=\"author\"
                                               placeholder=\"Enter author name (leave empty for 'Capitol Academy Analyst')...\"
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted\">
                                            Leave empty to automatically set as \"Capitol Academy Analyst\"
                                        </small>
                                    </div>
                                </div>

                                <!-- Published Date -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"published_date\" class=\"form-label\">
                                            <i class=\"fas fa-calendar text-primary mr-1\"></i>
                                            Published Date <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"date\"
                                               class=\"form-control enhanced-field\"
                                               id=\"published_date\"
                                               name=\"published_date\"
                                               value=\"";
        // line 146
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate("now", "Y-m-d"), "html", null, true);
        yield "\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a published date.
                                        </div>
                                    </div>
                                </div>
                            </div>



                            <!-- Excerpt -->
                            <div class=\"form-group\">
                                <label for=\"excerpt\" class=\"form-label\">
                                    <i class=\"fas fa-quote-left text-primary mr-1\"></i>
                                    Excerpt <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"excerpt\"
                                          name=\"excerpt\"
                                          rows=\"3\"
                                          placeholder=\"Enter a brief summary of the analysis...\"
                                          required
                                          maxlength=\"500\"
                                          style=\"min-height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\"></textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide an excerpt.
                                </div>
                            </div>

                            <!-- Content -->
                            <div class=\"form-group\">
                                <label for=\"content\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Content <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field rich-text-editor\"
                                          id=\"content\"
                                          name=\"content\"
                                          rows=\"12\"
                                          placeholder=\"Enter detailed analysis content...\"
                                          required
                                          style=\"min-height: 300px; font-size: 1rem; border: 2px solid #ced4da;\"></textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide content for the analysis.
                                </div>
                                <small class=\"form-text text-muted\">
                                    Use the rich text editor to format your content with colors, fonts, and styling.
                                </small>
                            </div>

                            <!-- Image Upload Row -->
                            <div class=\"row\">
                                <!-- Thumbnail Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"thumbnailImage\" class=\"form-label\">
                                            <i class=\"fas fa-image text-primary mr-1\"></i>
                                            Thumbnail Image
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control enhanced-field\"
                                               id=\"thumbnailImage\"
                                               name=\"thumbnailImage\"
                                               accept=\"image/*\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted\">
                                            For article cards. Recommended size: 400x300px
                                        </small>

                                        <!-- Thumbnail Preview -->
                                        <div id=\"thumbnail-preview\" class=\"mt-3 d-flex justify-content-center\" style=\"display: none;\">
                                            <div class=\"card\" style=\"max-width: 250px;\">
                                                <img id=\"thumbnail-preview-img\" src=\"\" alt=\"Thumbnail Preview\" class=\"card-img-top\" style=\"height: 150px; object-fit: cover;\">
                                                <div class=\"card-body p-2 text-center\">
                                                    <small class=\"text-muted\">Thumbnail Preview</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Featured Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"featured_image\" class=\"form-label\">
                                            <i class=\"fas fa-image text-primary mr-1\"></i>
                                            Featured Image
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control enhanced-field\"
                                               id=\"featured_image\"
                                               name=\"featured_image\"
                                               accept=\"image/*\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted\">
                                            For article detail page. Recommended size: 1200x600px
                                        </small>

                                        <!-- Featured Image Preview -->
                                        <div id=\"featured-preview\" class=\"mt-3 d-flex justify-content-center\" style=\"display: none;\">
                                            <div class=\"card\" style=\"max-width: 250px;\">
                                                <img id=\"featured-preview-img\" src=\"\" alt=\"Featured Preview\" class=\"card-img-top\" style=\"height: 150px; object-fit: cover;\">
                                                <div class=\"card-body p-2 text-center\">
                                                    <small class=\"text-muted\">Featured Preview</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Create Analysis
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"";
        // line 271
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_index");
        yield "\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>

";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 284
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 285
        yield "<style>
/* Enhanced Form Field Styling */
.enhanced-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px);
}

.enhanced-dropdown {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-dropdown:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
    transform: translateY(-1px);
}

/* Select2 Custom Styling - Asset Type Field */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    min-height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: #ffffff !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
    line-height: calc(1.6em + 1.25rem + 4px) !important;
    color: #495057 !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
    height: calc(1.6em + 1.25rem + 4px) !important;
    right: 0.75rem !important;
    top: 0 !important;
}

.select2-container--bootstrap4.select2-container--focus .select2-selection--single {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    transform: translateY(-1px) !important;
}
    align-items: center !important;
    height: calc(1.6em + 1.25rem + 4px) !important;
}

.select2-container--bootstrap4 .select2-selection--single:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.select2-container--bootstrap4 .select2-dropdown {
    border: 2px solid #1e3c72 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.15) !important;
}

.select2-container--bootstrap4 .select2-results__option--highlighted {
    background-color: #1e3c72 !important;
    color: white !important;
}

/* Ensure Select2 containers match form field heights exactly */
.select2-container {
    height: calc(1.6em + 1.25rem + 4px) !important;
}

.select2-container .select2-selection {
    height: calc(1.6em + 1.25rem + 4px) !important;
}

/* Form group focus styling */
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control,
.form-group.focused .form-select {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
}

/* Accessibility improvements */
/* Select2 height matching */
.select2-container .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    font-size: 1rem !important;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    line-height: calc(1.6em + 1.25rem) !important;
    padding-left: 12px !important;
    padding-right: 20px !important;
}

.select2-container .select2-selection--single .select2-selection__arrow {
    height: calc(1.6em + 1.25rem + 2px) !important;
    right: 1px !important;
}

@media (prefers-reduced-motion: reduce) {
    .enhanced-field,
    .enhanced-dropdown {
        transition: none !important;
        animation: none !important;
        transform: none !important;
    }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 412
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 413
        yield "<!-- Include Select2 for enhanced dropdowns -->
<link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
<link href=\"https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css\" rel=\"stylesheet\" />
<script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

<!-- Include CKEditor for rich text editing -->
<script src=\"https://cdn.ckeditor.com/ckeditor5/40.0.0/classic/ckeditor.js\"></script>

<script>
\$(document).ready(function() {
    // Enhanced asset type selection with search functionality
    const assetTypeSelect = document.getElementById('asset_type');
    if (assetTypeSelect) {
        \$(assetTypeSelect).select2({
            placeholder: 'Search and select an asset type...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });

        // Fix height after Select2 initialization
        setTimeout(function() {
            \$('.select2-container--bootstrap4 .select2-selection--single').css({
                'height': 'calc(1.6em + 1.25rem + 4px)',
                'min-height': 'calc(1.6em + 1.25rem + 4px)',
                'line-height': 'calc(1.6em + 1.25rem + 4px)',
                'border': '2px solid #ced4da',
                'border-radius': '8px'
            });
            \$('.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered').css({
                'line-height': 'calc(1.6em + 1.25rem + 4px)',
                'padding-left': '0.75rem',
                'padding-right': '0.75rem'
            });
            \$('.select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow').css({
                'height': 'calc(1.6em + 1.25rem + 4px)',
                'right': '0.75rem'
            });
        }, 100);
    }

    // Initialize CKEditor for content field
    let contentEditor;
    ClassicEditor
        .create(document.querySelector('#content'), {
            toolbar: {
                items: [
                    'undo', 'redo',
                    '|', 'heading',
                    '|', 'fontSize', 'fontFamily',
                    '|', 'bold', 'italic', 'underline',
                    '|', 'fontColor', 'fontBackgroundColor',
                    '|', 'link', 'insertTable', 'blockQuote',
                    '|', 'bulletedList', 'numberedList', 'outdent', 'indent',
                    '|', 'alignment',
                    '|', 'removeFormat'
                ]
            },
            fontSize: {
                options: [
                    9, 10, 11, 12, 'default', 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72
                ],
                supportAllValues: true
            },
            fontFamily: {
                options: [
                    'default',
                    'Arial, Helvetica, sans-serif',
                    'Courier New, Courier, monospace',
                    'Georgia, serif',
                    'Lucida Sans Unicode, Lucida Grande, sans-serif',
                    'Tahoma, Geneva, sans-serif',
                    'Times New Roman, Times, serif',
                    'Trebuchet MS, Helvetica, sans-serif',
                    'Verdana, Geneva, sans-serif'
                ],
                supportAllValues: true
            },
            heading: {
                options: [
                    { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                    { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                    { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                    { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
                ]
            },
            table: {
                contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells']
            }
        })
        .then(editor => {
            contentEditor = editor;
            // Set minimum height
            editor.editing.view.change(writer => {
                writer.setStyle('min-height', '300px', editor.editing.view.document.getRoot());
            });
        })
        .catch(error => {
            console.error('CKEditor initialization error:', error);
        });

    // Form field focus effects
    \$('.form-control, .enhanced-field').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Thumbnail preview functionality
    \$('#thumbnailImage').on('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                \$('#thumbnail-preview-img').attr('src', e.target.result);
                \$('#thumbnail-preview').show();
            };
            reader.readAsDataURL(file);
        } else {
            \$('#thumbnail-preview').hide();
        }
    });

    // Featured image preview functionality
    \$('#featured_image').on('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                \$('#featured-preview-img').attr('src', e.target.result);
                \$('#featured-preview').show();
            };
            reader.readAsDataURL(file);
        } else {
            \$('#featured-preview').hide();
        }
    });

    // Form validation
    const form = document.querySelector('.needs-validation');
    const submitBtn = form.querySelector('button[type=\"submit\"]');

    form.addEventListener('submit', function(event) {
        // Update CKEditor content before validation
        if (contentEditor) {
            document.querySelector('#content').value = contentEditor.getData();
        }

        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
            resetSubmitButton();
        } else {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Creating Analysis...';
        }
        form.classList.add('was-validated');
    });

    function resetSubmitButton() {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Create Analysis';
    }

    // Reset button state on page load
    resetSubmitButton();
});
</script>

<!-- TinyMCE Rich Text Editor -->
<script src=\"https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js\" referrerpolicy=\"origin\"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    tinymce.init({
        selector: '#content',
        height: 400,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount', 'textcolor', 'colorpicker'
        ],
        toolbar: 'undo redo | blocks | ' +
            'bold italic underline strikethrough | forecolor backcolor | ' +
            'alignleft aligncenter alignright alignjustify | ' +
            'bullist numlist outdent indent | ' +
            'removeformat | help',
        content_style: `
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 14px;
                line-height: 1.6;
                color: #011a2d;
            }
            p { margin-bottom: 1rem; }
            h1, h2, h3, h4, h5, h6 {
                color: #011a2d;
                font-weight: 600;
                margin-bottom: 0.5rem;
            }
        `,
        skin: 'oxide',
        content_css: 'default',
        branding: false,
        promotion: false,
        setup: function (editor) {
            editor.on('change', function () {
                editor.save();
            });
        },
        color_map: [
            '#011a2d', 'Capitol Navy',
            '#a90418', 'Capitol Red',
            '#000000', 'Black',
            '#434343', 'Dark Gray',
            '#666666', 'Gray',
            '#999999', 'Light Gray',
            '#b7b7b7', 'Lighter Gray',
            '#cccccc', 'Very Light Gray',
            '#d9d9d9', 'Lightest Gray',
            '#efefef', 'Off White',
            '#f3f3f3', 'Light Background',
            '#ffffff', 'White',
            '#980000', 'Dark Red',
            '#ff0000', 'Red',
            '#ff9900', 'Orange',
            '#ffff00', 'Yellow',
            '#00ff00', 'Green',
            '#00ffff', 'Cyan',
            '#4a86e8', 'Light Blue',
            '#0000ff', 'Blue',
            '#9900ff', 'Purple',
            '#ff00ff', 'Magenta'
        ],
        font_size_formats: '8pt 10pt 12pt 14pt 16pt 18pt 24pt 36pt 48pt',
        block_formats: 'Paragraph=p; Heading 1=h1; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6; Preformatted=pre'
    });
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/market_analysis/create.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  638 => 413,  625 => 412,  489 => 285,  476 => 284,  453 => 271,  325 => 146,  233 => 57,  216 => 43,  200 => 29,  190 => 25,  187 => 24,  183 => 23,  180 => 22,  170 => 18,  167 => 17,  163 => 16,  159 => 14,  146 => 13,  132 => 9,  127 => 8,  114 => 7,  91 => 5,  68 => 3,  45 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Create Market Analysis - Capitol Academy Admin{% endblock %}

{% block page_title %}Create New Market Analysis{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_market_analysis_index') }}\">Market Analysis</a></li>
<li class=\"breadcrumb-item active\">Create Analysis</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-chart-line mr-3\" style=\"font-size: 2rem;\"></i>
                        Create New Market Analysis
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Market Analysis Button -->
                        <a href=\"{{ path('admin_market_analysis_index') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Market Analysis
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('market_analysis_create') }}\">
            <input type=\"hidden\" name=\"is_active\" value=\"1\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Title and Asset Type Row -->
                            <div class=\"row\">
                                <!-- Analysis Title -->
                                <div class=\"col-md-8\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-heading text-primary mr-1\"></i>
                                            Analysis Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               placeholder=\"Enter analysis title...\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide an analysis title.
                                        </div>
                                    </div>
                                </div>

                                <!-- Asset Type -->
                                <div class=\"col-md-4\">
                                    <div class=\"form-group\">
                                        <label for=\"asset_type\" class=\"form-label\">
                                            <i class=\"fas fa-chart-bar text-primary mr-1\"></i>
                                            Asset Type <span class=\"text-danger\">*</span>
                                        </label>
                                        <select class=\"form-select enhanced-field\"
                                                id=\"asset_type\"
                                                name=\"asset_type\"
                                                required
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; padding: 0.75rem 2.25rem 0.75rem 0.75rem;\">
                                            <option value=\"\">Select Asset Type</option>
                                            <option value=\"stocks\">Stocks</option>
                                            <option value=\"forex\">Forex</option>
                                            <option value=\"crypto\">Crypto</option>
                                            <option value=\"crude_oil\">Crude Oil</option>
                                            <option value=\"gold\">Gold</option>
                                            <option value=\"commodities\">Commodities</option>
                                        </select>
                                        <div class=\"invalid-feedback\">
                                            Please select an asset type.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Author and Date Row -->
                            <div class=\"row\">
                                <!-- Author -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"author\" class=\"form-label\">
                                            <i class=\"fas fa-user-edit text-primary mr-1\"></i>
                                            Author
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"author\"
                                               name=\"author\"
                                               placeholder=\"Enter author name (leave empty for 'Capitol Academy Analyst')...\"
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted\">
                                            Leave empty to automatically set as \"Capitol Academy Analyst\"
                                        </small>
                                    </div>
                                </div>

                                <!-- Published Date -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"published_date\" class=\"form-label\">
                                            <i class=\"fas fa-calendar text-primary mr-1\"></i>
                                            Published Date <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"date\"
                                               class=\"form-control enhanced-field\"
                                               id=\"published_date\"
                                               name=\"published_date\"
                                               value=\"{{ 'now'|date('Y-m-d') }}\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a published date.
                                        </div>
                                    </div>
                                </div>
                            </div>



                            <!-- Excerpt -->
                            <div class=\"form-group\">
                                <label for=\"excerpt\" class=\"form-label\">
                                    <i class=\"fas fa-quote-left text-primary mr-1\"></i>
                                    Excerpt <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"excerpt\"
                                          name=\"excerpt\"
                                          rows=\"3\"
                                          placeholder=\"Enter a brief summary of the analysis...\"
                                          required
                                          maxlength=\"500\"
                                          style=\"min-height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\"></textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide an excerpt.
                                </div>
                            </div>

                            <!-- Content -->
                            <div class=\"form-group\">
                                <label for=\"content\" class=\"form-label\">
                                    <i class=\"fas fa-align-left text-primary mr-1\"></i>
                                    Content <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field rich-text-editor\"
                                          id=\"content\"
                                          name=\"content\"
                                          rows=\"12\"
                                          placeholder=\"Enter detailed analysis content...\"
                                          required
                                          style=\"min-height: 300px; font-size: 1rem; border: 2px solid #ced4da;\"></textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide content for the analysis.
                                </div>
                                <small class=\"form-text text-muted\">
                                    Use the rich text editor to format your content with colors, fonts, and styling.
                                </small>
                            </div>

                            <!-- Image Upload Row -->
                            <div class=\"row\">
                                <!-- Thumbnail Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"thumbnailImage\" class=\"form-label\">
                                            <i class=\"fas fa-image text-primary mr-1\"></i>
                                            Thumbnail Image
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control enhanced-field\"
                                               id=\"thumbnailImage\"
                                               name=\"thumbnailImage\"
                                               accept=\"image/*\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted\">
                                            For article cards. Recommended size: 400x300px
                                        </small>

                                        <!-- Thumbnail Preview -->
                                        <div id=\"thumbnail-preview\" class=\"mt-3 d-flex justify-content-center\" style=\"display: none;\">
                                            <div class=\"card\" style=\"max-width: 250px;\">
                                                <img id=\"thumbnail-preview-img\" src=\"\" alt=\"Thumbnail Preview\" class=\"card-img-top\" style=\"height: 150px; object-fit: cover;\">
                                                <div class=\"card-body p-2 text-center\">
                                                    <small class=\"text-muted\">Thumbnail Preview</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Featured Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"featured_image\" class=\"form-label\">
                                            <i class=\"fas fa-image text-primary mr-1\"></i>
                                            Featured Image
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control enhanced-field\"
                                               id=\"featured_image\"
                                               name=\"featured_image\"
                                               accept=\"image/*\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <small class=\"form-text text-muted\">
                                            For article detail page. Recommended size: 1200x600px
                                        </small>

                                        <!-- Featured Image Preview -->
                                        <div id=\"featured-preview\" class=\"mt-3 d-flex justify-content-center\" style=\"display: none;\">
                                            <div class=\"card\" style=\"max-width: 250px;\">
                                                <img id=\"featured-preview-img\" src=\"\" alt=\"Featured Preview\" class=\"card-img-top\" style=\"height: 150px; object-fit: cover;\">
                                                <div class=\"card-body p-2 text-center\">
                                                    <small class=\"text-muted\">Featured Preview</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class=\"card-footer\" style=\"background: #f8f9fa; border-top: 1px solid #dee2e6;\">
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <button type=\"submit\" class=\"btn btn-lg\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;\">
                                <i class=\"fas fa-save mr-2\"></i>
                                Create Analysis
                            </button>
                        </div>
                        <div class=\"col-md-6 text-right\">
                            <a href=\"{{ path('admin_market_analysis_index') }}\" class=\"btn btn-secondary btn-lg\">
                                <i class=\"fas fa-times mr-2\"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>

{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced Form Field Styling */
.enhanced-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px);
}

.enhanced-dropdown {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-dropdown:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
    transform: translateY(-1px);
}

/* Select2 Custom Styling - Asset Type Field */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    min-height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: #ffffff !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
    line-height: calc(1.6em + 1.25rem + 4px) !important;
    color: #495057 !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
    height: calc(1.6em + 1.25rem + 4px) !important;
    right: 0.75rem !important;
    top: 0 !important;
}

.select2-container--bootstrap4.select2-container--focus .select2-selection--single {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    transform: translateY(-1px) !important;
}
    align-items: center !important;
    height: calc(1.6em + 1.25rem + 4px) !important;
}

.select2-container--bootstrap4 .select2-selection--single:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.select2-container--bootstrap4 .select2-dropdown {
    border: 2px solid #1e3c72 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.15) !important;
}

.select2-container--bootstrap4 .select2-results__option--highlighted {
    background-color: #1e3c72 !important;
    color: white !important;
}

/* Ensure Select2 containers match form field heights exactly */
.select2-container {
    height: calc(1.6em + 1.25rem + 4px) !important;
}

.select2-container .select2-selection {
    height: calc(1.6em + 1.25rem + 4px) !important;
}

/* Form group focus styling */
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control,
.form-group.focused .form-select {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
}

/* Accessibility improvements */
/* Select2 height matching */
.select2-container .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    font-size: 1rem !important;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    line-height: calc(1.6em + 1.25rem) !important;
    padding-left: 12px !important;
    padding-right: 20px !important;
}

.select2-container .select2-selection--single .select2-selection__arrow {
    height: calc(1.6em + 1.25rem + 2px) !important;
    right: 1px !important;
}

@media (prefers-reduced-motion: reduce) {
    .enhanced-field,
    .enhanced-dropdown {
        transition: none !important;
        animation: none !important;
        transform: none !important;
    }
}
</style>
{% endblock %}

{% block javascripts %}
<!-- Include Select2 for enhanced dropdowns -->
<link href=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css\" rel=\"stylesheet\" />
<link href=\"https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css\" rel=\"stylesheet\" />
<script src=\"https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js\"></script>

<!-- Include CKEditor for rich text editing -->
<script src=\"https://cdn.ckeditor.com/ckeditor5/40.0.0/classic/ckeditor.js\"></script>

<script>
\$(document).ready(function() {
    // Enhanced asset type selection with search functionality
    const assetTypeSelect = document.getElementById('asset_type');
    if (assetTypeSelect) {
        \$(assetTypeSelect).select2({
            placeholder: 'Search and select an asset type...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });

        // Fix height after Select2 initialization
        setTimeout(function() {
            \$('.select2-container--bootstrap4 .select2-selection--single').css({
                'height': 'calc(1.6em + 1.25rem + 4px)',
                'min-height': 'calc(1.6em + 1.25rem + 4px)',
                'line-height': 'calc(1.6em + 1.25rem + 4px)',
                'border': '2px solid #ced4da',
                'border-radius': '8px'
            });
            \$('.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered').css({
                'line-height': 'calc(1.6em + 1.25rem + 4px)',
                'padding-left': '0.75rem',
                'padding-right': '0.75rem'
            });
            \$('.select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow').css({
                'height': 'calc(1.6em + 1.25rem + 4px)',
                'right': '0.75rem'
            });
        }, 100);
    }

    // Initialize CKEditor for content field
    let contentEditor;
    ClassicEditor
        .create(document.querySelector('#content'), {
            toolbar: {
                items: [
                    'undo', 'redo',
                    '|', 'heading',
                    '|', 'fontSize', 'fontFamily',
                    '|', 'bold', 'italic', 'underline',
                    '|', 'fontColor', 'fontBackgroundColor',
                    '|', 'link', 'insertTable', 'blockQuote',
                    '|', 'bulletedList', 'numberedList', 'outdent', 'indent',
                    '|', 'alignment',
                    '|', 'removeFormat'
                ]
            },
            fontSize: {
                options: [
                    9, 10, 11, 12, 'default', 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72
                ],
                supportAllValues: true
            },
            fontFamily: {
                options: [
                    'default',
                    'Arial, Helvetica, sans-serif',
                    'Courier New, Courier, monospace',
                    'Georgia, serif',
                    'Lucida Sans Unicode, Lucida Grande, sans-serif',
                    'Tahoma, Geneva, sans-serif',
                    'Times New Roman, Times, serif',
                    'Trebuchet MS, Helvetica, sans-serif',
                    'Verdana, Geneva, sans-serif'
                ],
                supportAllValues: true
            },
            heading: {
                options: [
                    { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                    { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                    { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                    { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
                ]
            },
            table: {
                contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells']
            }
        })
        .then(editor => {
            contentEditor = editor;
            // Set minimum height
            editor.editing.view.change(writer => {
                writer.setStyle('min-height', '300px', editor.editing.view.document.getRoot());
            });
        })
        .catch(error => {
            console.error('CKEditor initialization error:', error);
        });

    // Form field focus effects
    \$('.form-control, .enhanced-field').on('focus', function() {
        \$(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        \$(this).closest('.form-group').removeClass('focused');
    });

    // Thumbnail preview functionality
    \$('#thumbnailImage').on('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                \$('#thumbnail-preview-img').attr('src', e.target.result);
                \$('#thumbnail-preview').show();
            };
            reader.readAsDataURL(file);
        } else {
            \$('#thumbnail-preview').hide();
        }
    });

    // Featured image preview functionality
    \$('#featured_image').on('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                \$('#featured-preview-img').attr('src', e.target.result);
                \$('#featured-preview').show();
            };
            reader.readAsDataURL(file);
        } else {
            \$('#featured-preview').hide();
        }
    });

    // Form validation
    const form = document.querySelector('.needs-validation');
    const submitBtn = form.querySelector('button[type=\"submit\"]');

    form.addEventListener('submit', function(event) {
        // Update CKEditor content before validation
        if (contentEditor) {
            document.querySelector('#content').value = contentEditor.getData();
        }

        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
            resetSubmitButton();
        } else {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i>Creating Analysis...';
        }
        form.classList.add('was-validated');
    });

    function resetSubmitButton() {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class=\"fas fa-save mr-2\"></i>Create Analysis';
    }

    // Reset button state on page load
    resetSubmitButton();
});
</script>

<!-- TinyMCE Rich Text Editor -->
<script src=\"https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js\" referrerpolicy=\"origin\"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    tinymce.init({
        selector: '#content',
        height: 400,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount', 'textcolor', 'colorpicker'
        ],
        toolbar: 'undo redo | blocks | ' +
            'bold italic underline strikethrough | forecolor backcolor | ' +
            'alignleft aligncenter alignright alignjustify | ' +
            'bullist numlist outdent indent | ' +
            'removeformat | help',
        content_style: `
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 14px;
                line-height: 1.6;
                color: #011a2d;
            }
            p { margin-bottom: 1rem; }
            h1, h2, h3, h4, h5, h6 {
                color: #011a2d;
                font-weight: 600;
                margin-bottom: 0.5rem;
            }
        `,
        skin: 'oxide',
        content_css: 'default',
        branding: false,
        promotion: false,
        setup: function (editor) {
            editor.on('change', function () {
                editor.save();
            });
        },
        color_map: [
            '#011a2d', 'Capitol Navy',
            '#a90418', 'Capitol Red',
            '#000000', 'Black',
            '#434343', 'Dark Gray',
            '#666666', 'Gray',
            '#999999', 'Light Gray',
            '#b7b7b7', 'Lighter Gray',
            '#cccccc', 'Very Light Gray',
            '#d9d9d9', 'Lightest Gray',
            '#efefef', 'Off White',
            '#f3f3f3', 'Light Background',
            '#ffffff', 'White',
            '#980000', 'Dark Red',
            '#ff0000', 'Red',
            '#ff9900', 'Orange',
            '#ffff00', 'Yellow',
            '#00ff00', 'Green',
            '#00ffff', 'Cyan',
            '#4a86e8', 'Light Blue',
            '#0000ff', 'Blue',
            '#9900ff', 'Purple',
            '#ff00ff', 'Magenta'
        ],
        font_size_formats: '8pt 10pt 12pt 14pt 16pt 18pt 24pt 36pt 48pt',
        block_formats: 'Paragraph=p; Heading 1=h1; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6; Preformatted=pre'
    });
});
</script>
{% endblock %}
", "admin/market_analysis/create.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\market_analysis\\create.html.twig");
    }
}
