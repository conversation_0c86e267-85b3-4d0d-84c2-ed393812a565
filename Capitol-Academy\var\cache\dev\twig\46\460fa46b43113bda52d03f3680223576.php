<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* security/reset_password.html.twig */
class __TwigTemplate_10c322f9eebef9bb25e4f003973c65fa extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "security/reset_password.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "security/reset_password.html.twig"));

        // line 1
        yield "<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Capitol Academy - Reset Password</title>

    <!-- Google Fonts -->
    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap\" rel=\"stylesheet\">
    <!-- Font Awesome -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">
    <!-- Bootstrap 5 -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">

    <style>
        :root {
            --ca-primary: #011a2d;
            --ca-accent-red: #a90418;
            --ca-white: #ffffff;
            --ca-light-gray: #F6F7F9;
            --ca-neutral-dark: #343a40;
            --ca-neutral-medium: #6c757d;
            --ca-neutral-light: #f8f9fa;
            --ca-focus-blue: #007bff;
            --ca-success-green: #28a745;
            --ca-warning-yellow: #ffc107;
            --ca-danger-red: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--ca-primary) 0%, #1a3461 50%, #2a4a7a 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"dots\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><circle cx=\"10\" cy=\"10\" r=\"1.5\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23dots)\"/></svg>');
            opacity: 0.3;
        }

        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.05)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        .login-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            max-width: 1000px;
            width: 100%;
            background: var(--ca-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            min-height: 600px;
            position: relative;
            z-index: 1;
        }

        .login-branding {
            background: linear-gradient(135deg, var(--ca-primary) 0%, #1a3461 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 60px 40px;
            position: relative;
            overflow: hidden;
        }

        .login-branding::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"brand-pattern\" width=\"40\" height=\"40\" patternUnits=\"userSpaceOnUse\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23brand-pattern)\"/></svg>');
            opacity: 0.5;
        }

        .branding-content {
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .logo-section {
            margin-bottom: 30px;
        }

        .brand-logo-round {
            width: 120px;
            height: 120px;
            margin-bottom: 25px;
            filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));
            transition: transform 0.3s ease;
        }

        .brand-logo-round:hover {
            transform: scale(1.05);
        }

        .brand-title {
            color: var(--ca-white);
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: -0.5px;
        }

        .login-form-section {
            padding: 60px 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--ca-white);
        }

        .form-container {
            width: 100%;
            max-width: 400px;
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .form-title {
            color: var(--ca-primary);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }

        .form-subtitle {
            color: var(--ca-neutral-medium);
            font-size: 1.1rem;
            font-weight: 400;
            line-height: 1.5;
        }

        .alert {
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 25px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border-left: 4px solid var(--ca-danger-red);
        }

        .floating-label-group {
            position: relative;
            margin-bottom: 30px;
        }

        .floating-input {
            width: 100%;
            padding: 20px 16px 8px 16px;
            font-size: 1.1rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: var(--ca-white);
            transition: all 0.3s ease;
            font-family: inherit;
            color: var(--ca-neutral-dark);
        }

        .floating-input:focus {
            outline: none;
            border-color: var(--ca-focus-blue);
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            background: #fafbfc;
        }

        .floating-input:not(:placeholder-shown) {
            border-color: var(--ca-focus-blue);
        }

        .floating-label {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.1rem;
            color: var(--ca-neutral-medium);
            pointer-events: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .floating-input:focus + .floating-label,
        .floating-input:not(:placeholder-shown) + .floating-label {
            top: 12px;
            font-size: 0.85rem;
            color: var(--ca-focus-blue);
            font-weight: 600;
        }

        .input-border {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--ca-focus-blue);
            transition: width 0.3s ease;
        }

        .floating-input:focus ~ .input-border {
            width: 100%;
        }

        .login-btn {
            width: 100%;
            padding: 18px 24px;
            background: linear-gradient(135deg, var(--ca-primary) 0%, #1a3461 100%);
            color: var(--ca-white);
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 25px;
            text-transform: none;
            letter-spacing: 0.5px;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, #c41e3a 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(169, 4, 24, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .btn-icon {
            font-size: 1rem;
        }

        .form-footer {
            text-align: center;
            margin-top: 30px;
        }

        .back-link {
            color: var(--ca-neutral-medium);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .back-link:hover {
            color: var(--ca-focus-blue);
            text-decoration: none;
        }

        .code-input {
            text-align: center;
            font-size: 1.2rem;
            letter-spacing: 0.3rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .login-wrapper {
                grid-template-columns: 1fr;
                max-width: 500px;
                min-height: auto;
            }

            .login-branding {
                padding: 40px 30px;
            }

            .brand-logo-round {
                width: 80px;
                height: 80px;
                margin-bottom: 20px;
            }

            .brand-title {
                font-size: 2rem;
            }

            .login-form-section {
                padding: 40px 30px;
            }

            .form-title {
                font-size: 1.75rem;
            }

            .floating-input {
                padding: 18px 14px 6px 14px;
                font-size: 1rem;
            }

            .floating-label {
                font-size: 1rem;
                left: 14px;
            }

            .floating-input:focus + .floating-label,
            .floating-input:not(:placeholder-shown) + .floating-label {
                font-size: 0.8rem;
                top: 10px;
            }

            .login-btn {
                padding: 16px 20px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class=\"login-container\">
        <div class=\"login-wrapper\">
            <!-- Left Side - Branding -->
            <div class=\"login-branding\">
                <div class=\"branding-content\">
                    <div class=\"logo-section\">
                        <img src=\"";
        // line 380
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\" alt=\"Capitol Academy\" class=\"brand-logo-round\">
                        <h1 class=\"brand-title\">Capitol Academy</h1>
                    </div>
                </div>
            </div>

            <!-- Right Side - Password Reset Form -->
            <div class=\"login-form-section\">
                <div class=\"form-container\">
                    <!-- Form Header -->
                    <div class=\"form-header\">
                        <h2 class=\"form-title\">Reset Password</h2>
                        <p class=\"form-subtitle\">Enter your verification code and new password</p>
                    </div>

                    <!-- Error Messages -->
                    ";
        // line 396
        if ((($tmp = (isset($context["error"]) || array_key_exists("error", $context) ? $context["error"] : (function () { throw new RuntimeError('Variable "error" does not exist.', 396, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 397
            yield "                        <div class=\"alert alert-danger\">
                            <i class=\"fas fa-exclamation-triangle\"></i>
                            <span>";
            // line 399
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["error"]) || array_key_exists("error", $context) ? $context["error"] : (function () { throw new RuntimeError('Variable "error" does not exist.', 399, $this->source); })()), "html", null, true);
            yield "</span>
                        </div>
                    ";
        }
        // line 402
        yield "
                    <!-- Password Reset Form -->
                    <form method=\"post\" action=\"";
        // line 404
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_reset_password");
        yield "\">
                        <!-- Email Field (Hidden) -->
                        <input type=\"hidden\" name=\"email\" value=\"";
        // line 406
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["email"]) || array_key_exists("email", $context) ? $context["email"] : (function () { throw new RuntimeError('Variable "email" does not exist.', 406, $this->source); })()), "html", null, true);
        yield "\">

                        <!-- Code Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"text\"
                                   class=\"floating-input code-input\"
                                   name=\"code\"
                                   value=\"";
        // line 413
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["code"]) || array_key_exists("code", $context) ? $context["code"] : (function () { throw new RuntimeError('Variable "code" does not exist.', 413, $this->source); })()), "html", null, true);
        yield "\"
                                   placeholder=\" \"
                                   maxlength=\"6\"
                                   pattern=\"[0-9]{6}\"
                                   required>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-key\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                6-Digit Verification Code
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- New Password Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"password\"
                                   class=\"floating-input\"
                                   name=\"password\"
                                   placeholder=\" \"
                                   minlength=\"8\"
                                   required>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                New Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Confirm Password Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"password\"
                                   class=\"floating-input\"
                                   name=\"confirm_password\"
                                   placeholder=\" \"
                                   minlength=\"8\"
                                   required>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Confirm New Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- CSRF Token -->
                        <input type=\"hidden\" name=\"_token\" value=\"";
        // line 456
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("reset_password"), "html", null, true);
        yield "\">

                        <!-- Reset Password Button -->
                        <button type=\"submit\" class=\"login-btn\">
                            <i class=\"fas fa-key btn-icon\"></i>
                            <span>Reset Password</span>
                        </button>
                    </form>

                    <!-- Form Footer -->
                    <div class=\"form-footer\">
                        <a href=\"";
        // line 467
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_verify_reset_code", ["email" => (isset($context["email"]) || array_key_exists("email", $context) ? $context["email"] : (function () { throw new RuntimeError('Variable "email" does not exist.', 467, $this->source); })())]), "html", null, true);
        yield "\" class=\"back-link\">
                            <i class=\"fas fa-arrow-left me-2\"></i>Back to Code Verification
                        </a>
                        <div style=\"margin-top: 15px;\">
                            <a href=\"";
        // line 471
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_login");
        yield "\" style=\"color: var(--ca-focus-blue); text-decoration: none; font-weight: 600; transition: all 0.3s ease;\"
                               onmouseover=\"this.style.color='var(--ca-accent-red)'; this.style.textDecoration='underline'\"
                               onmouseout=\"this.style.color='var(--ca-focus-blue)'; this.style.textDecoration='none'\">
                                Back to Login
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);

        // Format code input to only accept numbers
        document.querySelector('.code-input').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });

        // Password confirmation validation
        const passwordField = document.querySelector('input[name=\"password\"]');
        const confirmPasswordField = document.querySelector('input[name=\"confirm_password\"]');

        function validatePasswordMatch() {
            if (confirmPasswordField.value && passwordField.value !== confirmPasswordField.value) {
                confirmPasswordField.setCustomValidity('Passwords do not match');
            } else {
                confirmPasswordField.setCustomValidity('');
            }
        }

        passwordField.addEventListener('input', validatePasswordMatch);
        confirmPasswordField.addEventListener('input', validatePasswordMatch);
    </script>
</body>
</html>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "security/reset_password.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  546 => 471,  539 => 467,  525 => 456,  479 => 413,  469 => 406,  464 => 404,  460 => 402,  454 => 399,  450 => 397,  448 => 396,  429 => 380,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Capitol Academy - Reset Password</title>

    <!-- Google Fonts -->
    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap\" rel=\"stylesheet\">
    <!-- Font Awesome -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">
    <!-- Bootstrap 5 -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">

    <style>
        :root {
            --ca-primary: #011a2d;
            --ca-accent-red: #a90418;
            --ca-white: #ffffff;
            --ca-light-gray: #F6F7F9;
            --ca-neutral-dark: #343a40;
            --ca-neutral-medium: #6c757d;
            --ca-neutral-light: #f8f9fa;
            --ca-focus-blue: #007bff;
            --ca-success-green: #28a745;
            --ca-warning-yellow: #ffc107;
            --ca-danger-red: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--ca-primary) 0%, #1a3461 50%, #2a4a7a 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"dots\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><circle cx=\"10\" cy=\"10\" r=\"1.5\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23dots)\"/></svg>');
            opacity: 0.3;
        }

        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.05)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        .login-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            max-width: 1000px;
            width: 100%;
            background: var(--ca-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            min-height: 600px;
            position: relative;
            z-index: 1;
        }

        .login-branding {
            background: linear-gradient(135deg, var(--ca-primary) 0%, #1a3461 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 60px 40px;
            position: relative;
            overflow: hidden;
        }

        .login-branding::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"brand-pattern\" width=\"40\" height=\"40\" patternUnits=\"userSpaceOnUse\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23brand-pattern)\"/></svg>');
            opacity: 0.5;
        }

        .branding-content {
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .logo-section {
            margin-bottom: 30px;
        }

        .brand-logo-round {
            width: 120px;
            height: 120px;
            margin-bottom: 25px;
            filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));
            transition: transform 0.3s ease;
        }

        .brand-logo-round:hover {
            transform: scale(1.05);
        }

        .brand-title {
            color: var(--ca-white);
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: -0.5px;
        }

        .login-form-section {
            padding: 60px 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--ca-white);
        }

        .form-container {
            width: 100%;
            max-width: 400px;
        }

        .form-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .form-title {
            color: var(--ca-primary);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: -0.5px;
        }

        .form-subtitle {
            color: var(--ca-neutral-medium);
            font-size: 1.1rem;
            font-weight: 400;
            line-height: 1.5;
        }

        .alert {
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 25px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border-left: 4px solid var(--ca-danger-red);
        }

        .floating-label-group {
            position: relative;
            margin-bottom: 30px;
        }

        .floating-input {
            width: 100%;
            padding: 20px 16px 8px 16px;
            font-size: 1.1rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: var(--ca-white);
            transition: all 0.3s ease;
            font-family: inherit;
            color: var(--ca-neutral-dark);
        }

        .floating-input:focus {
            outline: none;
            border-color: var(--ca-focus-blue);
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            background: #fafbfc;
        }

        .floating-input:not(:placeholder-shown) {
            border-color: var(--ca-focus-blue);
        }

        .floating-label {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.1rem;
            color: var(--ca-neutral-medium);
            pointer-events: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .floating-input:focus + .floating-label,
        .floating-input:not(:placeholder-shown) + .floating-label {
            top: 12px;
            font-size: 0.85rem;
            color: var(--ca-focus-blue);
            font-weight: 600;
        }

        .input-border {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--ca-focus-blue);
            transition: width 0.3s ease;
        }

        .floating-input:focus ~ .input-border {
            width: 100%;
        }

        .login-btn {
            width: 100%;
            padding: 18px 24px;
            background: linear-gradient(135deg, var(--ca-primary) 0%, #1a3461 100%);
            color: var(--ca-white);
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 25px;
            text-transform: none;
            letter-spacing: 0.5px;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, #c41e3a 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(169, 4, 24, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .btn-icon {
            font-size: 1rem;
        }

        .form-footer {
            text-align: center;
            margin-top: 30px;
        }

        .back-link {
            color: var(--ca-neutral-medium);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .back-link:hover {
            color: var(--ca-focus-blue);
            text-decoration: none;
        }

        .code-input {
            text-align: center;
            font-size: 1.2rem;
            letter-spacing: 0.3rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .login-wrapper {
                grid-template-columns: 1fr;
                max-width: 500px;
                min-height: auto;
            }

            .login-branding {
                padding: 40px 30px;
            }

            .brand-logo-round {
                width: 80px;
                height: 80px;
                margin-bottom: 20px;
            }

            .brand-title {
                font-size: 2rem;
            }

            .login-form-section {
                padding: 40px 30px;
            }

            .form-title {
                font-size: 1.75rem;
            }

            .floating-input {
                padding: 18px 14px 6px 14px;
                font-size: 1rem;
            }

            .floating-label {
                font-size: 1rem;
                left: 14px;
            }

            .floating-input:focus + .floating-label,
            .floating-input:not(:placeholder-shown) + .floating-label {
                font-size: 0.8rem;
                top: 10px;
            }

            .login-btn {
                padding: 16px 20px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class=\"login-container\">
        <div class=\"login-wrapper\">
            <!-- Left Side - Branding -->
            <div class=\"login-branding\">
                <div class=\"branding-content\">
                    <div class=\"logo-section\">
                        <img src=\"{{ asset('images/logos/logo-round.png') }}\" alt=\"Capitol Academy\" class=\"brand-logo-round\">
                        <h1 class=\"brand-title\">Capitol Academy</h1>
                    </div>
                </div>
            </div>

            <!-- Right Side - Password Reset Form -->
            <div class=\"login-form-section\">
                <div class=\"form-container\">
                    <!-- Form Header -->
                    <div class=\"form-header\">
                        <h2 class=\"form-title\">Reset Password</h2>
                        <p class=\"form-subtitle\">Enter your verification code and new password</p>
                    </div>

                    <!-- Error Messages -->
                    {% if error %}
                        <div class=\"alert alert-danger\">
                            <i class=\"fas fa-exclamation-triangle\"></i>
                            <span>{{ error }}</span>
                        </div>
                    {% endif %}

                    <!-- Password Reset Form -->
                    <form method=\"post\" action=\"{{ path('app_reset_password') }}\">
                        <!-- Email Field (Hidden) -->
                        <input type=\"hidden\" name=\"email\" value=\"{{ email }}\">

                        <!-- Code Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"text\"
                                   class=\"floating-input code-input\"
                                   name=\"code\"
                                   value=\"{{ code }}\"
                                   placeholder=\" \"
                                   maxlength=\"6\"
                                   pattern=\"[0-9]{6}\"
                                   required>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-key\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                6-Digit Verification Code
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- New Password Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"password\"
                                   class=\"floating-input\"
                                   name=\"password\"
                                   placeholder=\" \"
                                   minlength=\"8\"
                                   required>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                New Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Confirm Password Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"password\"
                                   class=\"floating-input\"
                                   name=\"confirm_password\"
                                   placeholder=\" \"
                                   minlength=\"8\"
                                   required>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Confirm New Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- CSRF Token -->
                        <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('reset_password') }}\">

                        <!-- Reset Password Button -->
                        <button type=\"submit\" class=\"login-btn\">
                            <i class=\"fas fa-key btn-icon\"></i>
                            <span>Reset Password</span>
                        </button>
                    </form>

                    <!-- Form Footer -->
                    <div class=\"form-footer\">
                        <a href=\"{{ path('app_verify_reset_code', {email: email}) }}\" class=\"back-link\">
                            <i class=\"fas fa-arrow-left me-2\"></i>Back to Code Verification
                        </a>
                        <div style=\"margin-top: 15px;\">
                            <a href=\"{{ path('app_login') }}\" style=\"color: var(--ca-focus-blue); text-decoration: none; font-weight: 600; transition: all 0.3s ease;\"
                               onmouseover=\"this.style.color='var(--ca-accent-red)'; this.style.textDecoration='underline'\"
                               onmouseout=\"this.style.color='var(--ca-focus-blue)'; this.style.textDecoration='none'\">
                                Back to Login
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);

        // Format code input to only accept numbers
        document.querySelector('.code-input').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });

        // Password confirmation validation
        const passwordField = document.querySelector('input[name=\"password\"]');
        const confirmPasswordField = document.querySelector('input[name=\"confirm_password\"]');

        function validatePasswordMatch() {
            if (confirmPasswordField.value && passwordField.value !== confirmPasswordField.value) {
                confirmPasswordField.setCustomValidity('Passwords do not match');
            } else {
                confirmPasswordField.setCustomValidity('');
            }
        }

        passwordField.addEventListener('input', validatePasswordMatch);
        confirmPasswordField.addEventListener('input', validatePasswordMatch);
    </script>
</body>
</html>
", "security/reset_password.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\security\\reset_password.html.twig");
    }
}
