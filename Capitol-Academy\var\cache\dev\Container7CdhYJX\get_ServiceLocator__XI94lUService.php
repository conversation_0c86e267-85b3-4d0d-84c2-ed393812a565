<?php

namespace Container7CdhYJX;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator__XI94lUService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator..XI94lU' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator..XI94lU'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'accessRepository' => ['privates', 'App\\Repository\\UserVideoAccessRepository', 'getUserVideoAccessRepositoryService', true],
            'courseRepository' => ['privates', 'App\\Repository\\CourseRepository', 'getCourseRepositoryService', true],
            'orderRepository' => ['privates', 'App\\Repository\\OrderRepository', 'getOrderRepositoryService', true],
            'planRepository' => ['privates', 'App\\Repository\\PlanRepository', 'getPlanRepositoryService', true],
            'userRepository' => ['privates', 'App\\Repository\\UserRepository', 'getUserRepositoryService', true],
            'videoRepository' => ['privates', 'App\\Repository\\VideoRepository', 'getVideoRepositoryService', true],
        ], [
            'accessRepository' => 'App\\Repository\\UserVideoAccessRepository',
            'courseRepository' => 'App\\Repository\\CourseRepository',
            'orderRepository' => 'App\\Repository\\OrderRepository',
            'planRepository' => 'App\\Repository\\PlanRepository',
            'userRepository' => 'App\\Repository\\UserRepository',
            'videoRepository' => 'App\\Repository\\VideoRepository',
        ]);
    }
}
