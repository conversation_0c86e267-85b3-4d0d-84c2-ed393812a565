{% extends 'admin/base.html.twig' %}

{% block title %}Contacts Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Contacts Management{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Contacts</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Contacts Management',
    'page_icon': 'fas fa-envelope',
    'search_placeholder': 'Search contacts by name, email, or country...',
    'stats': [
        {
            'title': 'Total Contacts',
            'value': contacts|length,
            'icon': 'fas fa-envelope',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Recent (7 days)',
            'value': contacts|filter(contact => contact.createdAt and contact.createdAt > date('-7 days'))|length,
            'icon': 'fas fa-envelope-open',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'This Month',
            'value': contacts|filter(contact => contact.createdAt and contact.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-calendar-alt',
            'color': '#17a2b8',
            'gradient': 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)'
        },
        {
            'title': 'Countries',
            'value': contacts|map(c => c.country)|reduce((carry, country) => country in carry ? carry : carry|merge([country]), [])|length,
            'icon': 'fas fa-globe',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Name'},
            {'text': 'Subject'},
            {'text': 'Country'},
            {'text': 'Source'},
            {'text': 'Date'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for contact in contacts %}
            {% set row_cells = [
                {
                    'content': '<h6 class="contact-name mb-0 font-weight-bold text-dark">' ~ contact.fullName ~ '</h6>'
                },
                {
                    'content': contact.subject ?
                        '<span class="contact-subject text-dark font-weight-medium">' ~ (contact.subject|length > 40 ? contact.subject|slice(0, 40) ~ '...' : contact.subject) ~ '</span>' :
                        '<span class="text-muted">No subject</span>'
                },
                {
                    'content': '<span class="contact-country text-dark">' ~ (contact.country|default('N/A')) ~ '</span>'
                },
                {
                    'content': '<span class="badge bg-info">' ~ (contact.sourcePage|default('Website')) ~ '</span>'
                },
                {
                    'content': '<small class="text-muted">' ~ contact.createdAt|date('M d, Y H:i') ~ '</small>'
                },
                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_contact_show', {'slug': contact.urlSlug}) ~ '" class="btn btn-sm shadow-sm" style="background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="View Contact"><i class="fas fa-eye"></i></a>
                        <a href="mailto:' ~ contact.email ~ '" class="btn btn-sm shadow-sm" style="background: #28a745; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Send Email"><i class="fas fa-envelope"></i></a>
                        <button type="button" class="btn btn-sm shadow-sm toggle-processed-btn" data-contact-slug="' ~ contact.urlSlug ~ '" style="background: ' ~ (contact.processed ? '#ffc107' : '#28a745') ~ '; color: ' ~ (contact.processed ? '#212529' : 'white') ~ '; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="' ~ (contact.processed ? 'Mark as Unprocessed' : 'Mark as Processed') ~ '"><i class="fas ' ~ (contact.processed ? 'fa-undo' : 'fa-check') ~ '"></i></button>
                        <button type="button" class="btn btn-sm shadow-sm delete-contact-btn" data-contact-slug="' ~ contact.urlSlug ~ '" data-contact-name="' ~ contact.fullName ~ '" style="background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Delete Contact"><i class="fas fa-trash"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'contact-row',
            'empty_message': 'No contacts found',
            'empty_icon': 'fas fa-envelope',
            'empty_description': 'Contact submissions will appear here.',
            'search_config': {
                'fields': ['.contact-name', '.contact-email', '.contact-country']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.contact-row',
        ['.contact-name', '.contact-email', '.contact-country']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Contact management functions
$(document).on('click', '.toggle-processed-btn', function() {
    const button = $(this);
    const contactSlug = button.data('contact-slug');
    const originalHtml = button.html();

    // Show loading state
    button.prop('disabled', true);
    button.html('<i class="fas fa-spinner fa-spin"></i>');

    // Make AJAX request
    $.ajax({
        url: `/admin/contacts/${contactSlug}/toggle-processed`,
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        success: function(response) {
            if (response.success) {
                // Remove success message - just reload the page
                setTimeout(function() {
                    location.reload();
                }, 500);
            } else {
                showCapitolAlert('error', response.message || 'An error occurred');
                button.prop('disabled', false);
                button.html(originalHtml);
            }
        },
        error: function(xhr) {
            let errorMessage = 'An error occurred while updating the contact status.';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to perform this action.';
            } else if (xhr.status === 404) {
                errorMessage = 'Contact not found.';
            }

            showCapitolAlert('error', errorMessage);
            button.prop('disabled', false);
            button.html(originalHtml);
        }
    });
});

$(document).on('click', '.delete-contact-btn', function() {
    const button = $(this);
    const contactSlug = button.data('contact-slug');
    const contactName = button.data('contact-name');

    // Use standardized delete modal
    showDeleteModal(contactName, function() {
        executeContactDelete(contactSlug, button);
    });
});

// Contact management functions using standardized modals
function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

function executeContactDelete(contactSlug, button) {
    // Show loading state
    button.prop('disabled', true);
    button.html('<i class="fas fa-spinner fa-spin"></i>');

    // Make AJAX request
    $.ajax({
        url: `/admin/contacts/${contactSlug}/delete`,
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        success: function(response) {
            if (response.success) {
                // Remove success message - just reload the page
                setTimeout(function() {
                    location.reload();
                }, 500);
            } else {
                showCapitolAlert('error', response.message || 'An error occurred');
                button.prop('disabled', false);
                button.html('<i class="fas fa-trash"></i>');
            }
        },
        error: function(xhr) {
            let errorMessage = 'An error occurred while deleting the contact.';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 403) {
                errorMessage = 'You do not have permission to perform this action.';
            } else if (xhr.status === 404) {
                errorMessage = 'Contact not found.';
            }

            showCapitolAlert('error', errorMessage);
            button.prop('disabled', false);
            button.html('<i class="fas fa-trash"></i>');
        }
    });
}

// Function to show Capitol Academy styled alert messages
function showCapitolAlert(type, message) {
    // Use Capitol Academy navy blue for success messages
    const alertStyle = type === 'success' ?
        'background: #011a2d; color: white; border: 1px solid #011a2d;' :
        'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;';

    const alertHtml = `
        <div class="alert alert-dismissible fade show" role="alert" style="${alertStyle} border-radius: 8px; margin-bottom: 1rem; position: relative; z-index: 1050;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close ${type === 'success' ? 'btn-close-white' : ''}" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    // Remove existing alerts to prevent duplicates
    $('.alert').remove();

    // Position alert below header cards but above table content
    // Look for the statistics cards section and place alert after it
    const statsSection = $('.card-body.pb-0');
    if (statsSection.length > 0) {
        // If stats section exists, place alert after it
        statsSection.after(alertHtml);
    } else {
        // Fallback: place after the card header
        $('.card-header').after('<div class="alert-container" style="padding: 0 1.5rem;">' + alertHtml + '</div>');
    }

    // Auto-dismiss after 2 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 2000);
}
</script>
{% endblock %}
