<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* security/login.html.twig */
class __TwigTemplate_43741fee5b40cbe59d454f6e4adf422f extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "security/login.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "security/login.html.twig"));

        // line 1
        yield "<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Capitol Academy - Login</title>

    <!-- Google Fonts -->
    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap\" rel=\"stylesheet\">
    <!-- Font Awesome -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">
    <!-- Bootstrap 5 -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">

    <style>
        /* Capitol Academy Brand Colors */
        :root {
            --ca-primary-blue: #011a2d;
            --ca-secondary-blue: #1a3461;
            --ca-accent-red: #a90418;
            --ca-accent-red-dark: #8b0314;
            --ca-dark-gray: #343a40;
            --ca-medium-gray: #6c757d;
            --ca-light-gray: #f8f9fa;
            --ca-white: #ffffff;
            --ca-success-green: #28a745;
            --ca-warning-orange: #ffc107;
            --ca-focus-blue: #011a2d;
            --ca-focus-blue-light: rgba(1, 26, 45, 0.1);
            --ca-focus-blue-border: rgba(1, 26, 45, 0.3);
            --ca-professional-shadow: rgba(1, 26, 45, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            overflow-x: hidden;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.05)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        .login-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            max-width: 1000px;
            width: 100%;
            background: var(--ca-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            min-height: 600px;
            position: relative;
            z-index: 1;
        }

        /* Left Side - Branding */
        .login-branding {
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            padding: 40px 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-branding::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.08)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .branding-content {
            position: relative;
            z-index: 2;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .logo {
            width: 85px;
            height: 85px;
            margin-bottom: 25px;
            filter: brightness(0) invert(1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            backdrop-filter: blur(10px);
        }

        .logo:hover {
            transform: scale(1.08) rotate(5deg);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
        }

        .logo-fallback {
            display: none;
            width: 85px;
            height: 85px;
            margin: 0 auto 25px;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--ca-white);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .brand-title {
            font-size: 2.8rem;
            font-weight: 800;
            margin: 0 0 15px 0;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
            letter-spacing: -0.5px;
            background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .brand-subtitle {
            font-size: 1.2rem;
            opacity: 0.95;
            margin: 0;
            font-weight: 500;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .features-list {
            margin-top: 50px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            font-size: 1.1rem;
            font-weight: 500;
            opacity: 0.95;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            opacity: 1;
            transform: translateX(5px);
        }

        .feature-item i {
            font-size: 1.5rem;
            margin-right: 20px;
            color: var(--ca-accent-red);
            width: 28px;
            text-align: center;
            background: rgba(169, 4, 24, 0.1);
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .feature-item:hover i {
            background: var(--ca-accent-red);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
        }

        /* Right Side - Form */
        .login-form-section {
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: linear-gradient(135deg, var(--ca-white) 0%, #fafbfc 100%);
        }

        .form-container {
            max-width: 360px;
            width: 100%;
            margin: 0 auto;
        }

        .form-header {
            text-align: center;
            margin-bottom: 35px;
            animation: fadeInUp 0.6s ease;
        }

        .form-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--ca-dark-gray);
            margin: 0 0 12px 0;
            letter-spacing: -0.3px;
        }

        .form-subtitle {
            color: var(--ca-medium-gray);
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
        }

        /* Floating Label Inputs */
        .floating-label-group {
            position: relative;
            margin-bottom: 28px;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }

        .floating-input {
            width: 100%;
            padding: 22px 0 12px 0;
            border: none;
            border-bottom: 2px solid #e8ecef;
            background: transparent;
            font-size: 1.05rem;
            color: var(--ca-dark-gray);
            outline: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            position: relative;
            height: calc(1.6em + 1.25rem + 4px);
        }

        .floating-input:focus {
            border-bottom-color: var(--ca-focus-blue);
            background: transparent !important;
            transform: translateY(-1px);
        }

        .floating-input:hover:not(:focus) {
            border-bottom-color: var(--ca-focus-blue-border);
        }

        .floating-input:focus::placeholder {
            opacity: 0;
        }

        .floating-label {
            position: absolute;
            top: 22px;
            left: 0;
            font-size: 1.05rem;
            color: var(--ca-medium-gray);
            pointer-events: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            transform-origin: left top;
        }

        .floating-input:focus + .floating-label,
        .floating-input:valid + .floating-label,
        .floating-input:not(:placeholder-shown) + .floating-label {
            top: -12px;
            font-size: 0.85rem;
            color: var(--ca-focus-blue);
            font-weight: 700;
            transform: scale(0.9);
            text-shadow: 0 1px 2px rgba(1, 26, 45, 0.1);
        }

        .floating-input:hover:not(:focus) + .floating-label {
            color: var(--ca-focus-blue-border);
        }

        .input-border {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(135deg, var(--ca-focus-blue) 0%, var(--ca-secondary-blue) 100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(1, 26, 45, 0.3);
        }

        .floating-input:focus ~ .input-border {
            width: 100%;
        }

        .floating-input:hover:not(:focus) ~ .input-border {
            width: 30%;
            background: linear-gradient(135deg, var(--ca-focus-blue-border) 0%, rgba(26, 52, 97, 0.5) 100%);
        }

        /* Login Button */
        .login-btn {
            width: 100%;
            padding: 18px 35px;
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, var(--ca-accent-red-dark) 100%);
            border: none;
            border-radius: 60px;
            color: var(--ca-white);
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 35px;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 25px rgba(169, 4, 24, 0.3);
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.6s;
            animation-fill-mode: both;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, var(--ca-accent-red-dark) 0%, var(--ca-accent-red) 100%);
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(169, 4, 24, 0.4);
        }

        .login-btn:focus {
            outline: 3px solid var(--ca-focus-blue-border);
            outline-offset: 3px;
        }

        .login-btn:active {
            transform: translateY(-1px);
        }

        .btn-icon {
            transition: transform 0.3s ease;
            font-size: 1.2rem;
        }

        .login-btn:hover .btn-icon {
            transform: translateX(8px);
        }

        /* Alerts */
        .alert {
            padding: 18px 24px;
            border-radius: 12px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
            font-size: 0.95rem;
            animation: slideInDown 0.5s ease;
        }

        .alert-danger {
            background: rgba(169, 4, 24, 0.1);
            color: var(--ca-accent-red-dark);
            border: 1px solid rgba(169, 4, 24, 0.2);
            border-left: 4px solid var(--ca-accent-red);
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            border: 1px solid rgba(40, 167, 69, 0.2);
            border-left: 4px solid var(--ca-success-green);
        }

        /* Form Footer */
        .form-footer {
            text-align: center;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.8s;
            animation-fill-mode: both;
        }

        .back-link, .register-link {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 8px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border: 2px solid;
            text-align: center;
            min-width: 140px;
        }

        .back-link {
            background: white;
            color: var(--ca-focus-blue);
            border-color: var(--ca-focus-blue);
        }

        .back-link:hover {
            background: var(--ca-focus-blue);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
        }

        .register-link {
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, var(--ca-accent-red-dark) 100%);
            color: white;
            border-color: var(--ca-accent-red);
        }

        .register-link:hover {
            background: linear-gradient(135deg, var(--ca-accent-red-dark) 0%, var(--ca-accent-red) 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
        }

        /* Simple text link for registration */
        .register-text-link {
            color: var(--ca-medium-gray);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            margin-top: 15px;
            display: block;
        }

        .register-text-link:hover {
            color: var(--ca-focus-blue);
            text-decoration: underline;
        }



        /* OAuth Divider */
        .oauth-divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.5s;
            animation-fill-mode: both;
        }

        .oauth-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }

        .oauth-divider span {
            background: var(--ca-white);
            padding: 0 15px;
            color: var(--ca-medium-gray);
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Google OAuth Button */
        .google-btn {
            width: 100%;
            padding: 15px 20px;
            font-size: 1rem;
            font-weight: 500;
            color: var(--ca-dark-gray);
            background: var(--ca-white);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 25px;
            text-decoration: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.7s;
            animation-fill-mode: both;
        }

        .google-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            border-color: #dadce0;
            color: var(--ca-dark-gray);
            text-decoration: none;
        }

        .google-btn:active {
            transform: translateY(0);
        }

        .google-icon {
            flex-shrink: 0;
        }

        .logo-section {
            text-align: center;
        }

        .brand-logo-round {
            width: 300px;
            height: 300px;
            margin-bottom: 20px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .login-wrapper {
                grid-template-columns: 1fr;
                max-width: 450px;
            }

            .login-branding {
                padding: 40px 30px;
                min-height: 300px;
            }

            .brand-title {
                font-size: 2.2rem;
            }

            .features-list {
                margin-top: 30px;
            }

            .feature-item {
                font-size: 1rem;
                margin-bottom: 20px;
            }

            .login-form-section {
                padding: 40px 30px;
            }

            .form-title {
                font-size: 1.8rem;
            }

            .floating-input {
                font-size: 1rem;
            }

            .login-btn {
                padding: 18px 30px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 15px;
            }

            .login-branding {
                padding: 30px 20px;
            }

            .login-form-section {
                padding: 30px 20px;
            }

            .brand-title {
                font-size: 1.8rem;
            }

            .floating-input {
                font-size: 1rem;
            }

            .login-btn {
                padding: 18px 30px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class=\"login-container\">
        <div class=\"login-wrapper\">
            <!-- Left Side - Branding -->
            <div class=\"login-branding\">
                <div class=\"branding-content\">
                    <div class=\"logo-section\">
                        <img src=\"";
        // line 689
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\" alt=\"Capitol Academy\" class=\"brand-logo-round\">
                        <h1 class=\"brand-title\">Capitol Academy</h1>
                    </div>
                </div>
            </div>

            <!-- Right Side - User Login Form -->
            <div class=\"login-form-section\">
                <div class=\"form-container\">
                    <!-- Form Header -->
                    <div class=\"form-header\">
                        <h2 class=\"form-title\">Welcome Back</h2>
                        <p class=\"form-subtitle\">Sign in to your account</p>
                    </div>

                    <!-- Error Messages -->
                    ";
        // line 705
        if ((($tmp = (isset($context["error"]) || array_key_exists("error", $context) ? $context["error"] : (function () { throw new RuntimeError('Variable "error" does not exist.', 705, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 706
            yield "                        <div class=\"alert alert-danger\">
                            <i class=\"fas fa-exclamation-triangle\"></i>
                            <span>";
            // line 708
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\TranslationExtension']->trans(CoreExtension::getAttribute($this->env, $this->source, (isset($context["error"]) || array_key_exists("error", $context) ? $context["error"] : (function () { throw new RuntimeError('Variable "error" does not exist.', 708, $this->source); })()), "messageKey", [], "any", false, false, false, 708), CoreExtension::getAttribute($this->env, $this->source, (isset($context["error"]) || array_key_exists("error", $context) ? $context["error"] : (function () { throw new RuntimeError('Variable "error" does not exist.', 708, $this->source); })()), "messageData", [], "any", false, false, false, 708), "security"), "html", null, true);
            yield "</span>
                        </div>
                    ";
        }
        // line 711
        yield "
                    <!-- Login Form -->
                    <form method=\"post\" action=\"";
        // line 713
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_login");
        yield "\">
                        <!-- Email Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"email\"
                                   class=\"floating-input\"
                                   name=\"_username\"
                                   value=\"";
        // line 719
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["last_username"]) || array_key_exists("last_username", $context) ? $context["last_username"] : (function () { throw new RuntimeError('Variable "last_username" does not exist.', 719, $this->source); })()), "html", null, true);
        yield "\"
                                   placeholder=\" \"
                                   required
                                   autofocus>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-envelope\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Email Address
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Password Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"password\"
                                   class=\"floating-input\"
                                   name=\"_password\"
                                   placeholder=\" \"
                                   required>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>



                        <!-- CSRF Token -->
                        <input type=\"hidden\" name=\"_token\" value=\"";
        // line 747
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("authenticate"), "html", null, true);
        yield "\">

                        <!-- Login Button -->
                        <button type=\"submit\" class=\"login-btn\">
                            <i class=\"fas fa-sign-in-alt btn-icon\"></i>
                            <span>Sign In</span>
                        </button>
                    </form>

                    <!-- Forgot Password Link -->
                    <div style=\"text-align: center; margin: 20px 0;\">
                        <a href=\"";
        // line 758
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_forgot_password");
        yield "\" style=\"color: var(--ca-focus-blue); text-decoration: none; font-weight: 600; font-size: 0.95rem; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.color='var(--ca-accent-red)'; this.style.textDecoration='underline'\"
                           onmouseout=\"this.style.color='var(--ca-focus-blue)'; this.style.textDecoration='none'\">
                            <i class=\"fas fa-key\" style=\"margin-right: 6px;\"></i>Forgot your password?
                        </a>
                    </div>

                    ";
        // line 766
        yield "                    ";
        $context["google_client_id"] = CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 766, $this->source); })()), "request", [], "any", false, false, false, 766), "server", [], "any", false, false, false, 766), "get", ["GOOGLE_OAUTH_CLIENT_ID"], "method", false, false, false, 766);
        // line 767
        yield "                    ";
        if (((isset($context["google_client_id"]) || array_key_exists("google_client_id", $context) ? $context["google_client_id"] : (function () { throw new RuntimeError('Variable "google_client_id" does not exist.', 767, $this->source); })()) && ((isset($context["google_client_id"]) || array_key_exists("google_client_id", $context) ? $context["google_client_id"] : (function () { throw new RuntimeError('Variable "google_client_id" does not exist.', 767, $this->source); })()) != "your_google_oauth_client_id_here"))) {
            // line 768
            yield "                    <!-- OAuth Divider -->
                    <div class=\"oauth-divider\">
                        <span>or</span>
                    </div>

                    <!-- Google OAuth Button -->
                    <a href=\"";
            // line 774
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("connect_google_start");
            yield "\" class=\"google-btn\">
                        <svg class=\"google-icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">
                            <path fill=\"#4285F4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>
                            <path fill=\"#34A853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>
                            <path fill=\"#FBBC05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>
                            <path fill=\"#EA4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>
                        </svg>
                        <span>Continue with Google</span>
                    </a>
                    ";
        }
        // line 784
        yield "
                    <!-- Form Footer -->
                    <div class=\"form-footer\">
                        <a href=\"";
        // line 787
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_home");
        yield "\" class=\"back-link\">
                            <i class=\"fas fa-arrow-left me-2\"></i>Back to Website
                        </a>
                        <div style=\"margin-top: 15px;\">
                            <a href=\"";
        // line 791
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_register");
        yield "\" style=\"color: var(--ca-focus-blue); text-decoration: none; font-weight: 600; transition: all 0.3s ease;\"
                               onmouseover=\"this.style.color='var(--ca-accent-red)'; this.style.textDecoration='underline'\"
                               onmouseout=\"this.style.color='var(--ca-focus-blue)'; this.style.textDecoration='none'\">
                                Don't have an account? Register here
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => alert.remove(), 300);
            });
        }, 5000);

        // Enhanced form interactions
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('.floating-input');

            inputs.forEach(input => {
                // Handle autofill detection
                const checkAutofill = () => {
                    if (input.value !== '') {
                        input.classList.add('has-value');
                    } else {
                        input.classList.remove('has-value');
                    }
                };

                input.addEventListener('input', checkAutofill);
                input.addEventListener('blur', checkAutofill);

                // Initial check
                setTimeout(checkAutofill, 100);
            });
        });
    </script>
</body>
</html>";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "security/login.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  876 => 791,  869 => 787,  864 => 784,  851 => 774,  843 => 768,  840 => 767,  837 => 766,  827 => 758,  813 => 747,  782 => 719,  773 => 713,  769 => 711,  763 => 708,  759 => 706,  757 => 705,  738 => 689,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Capitol Academy - Login</title>

    <!-- Google Fonts -->
    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap\" rel=\"stylesheet\">
    <!-- Font Awesome -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">
    <!-- Bootstrap 5 -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">

    <style>
        /* Capitol Academy Brand Colors */
        :root {
            --ca-primary-blue: #011a2d;
            --ca-secondary-blue: #1a3461;
            --ca-accent-red: #a90418;
            --ca-accent-red-dark: #8b0314;
            --ca-dark-gray: #343a40;
            --ca-medium-gray: #6c757d;
            --ca-light-gray: #f8f9fa;
            --ca-white: #ffffff;
            --ca-success-green: #28a745;
            --ca-warning-orange: #ffc107;
            --ca-focus-blue: #011a2d;
            --ca-focus-blue-light: rgba(1, 26, 45, 0.1);
            --ca-focus-blue-border: rgba(1, 26, 45, 0.3);
            --ca-professional-shadow: rgba(1, 26, 45, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            overflow-x: hidden;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.05)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        .login-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            max-width: 1000px;
            width: 100%;
            background: var(--ca-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            min-height: 600px;
            position: relative;
            z-index: 1;
        }

        /* Left Side - Branding */
        .login-branding {
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            padding: 40px 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-branding::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.08)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .branding-content {
            position: relative;
            z-index: 2;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .logo {
            width: 85px;
            height: 85px;
            margin-bottom: 25px;
            filter: brightness(0) invert(1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            backdrop-filter: blur(10px);
        }

        .logo:hover {
            transform: scale(1.08) rotate(5deg);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
        }

        .logo-fallback {
            display: none;
            width: 85px;
            height: 85px;
            margin: 0 auto 25px;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--ca-white);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .brand-title {
            font-size: 2.8rem;
            font-weight: 800;
            margin: 0 0 15px 0;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
            letter-spacing: -0.5px;
            background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .brand-subtitle {
            font-size: 1.2rem;
            opacity: 0.95;
            margin: 0;
            font-weight: 500;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .features-list {
            margin-top: 50px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            font-size: 1.1rem;
            font-weight: 500;
            opacity: 0.95;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            opacity: 1;
            transform: translateX(5px);
        }

        .feature-item i {
            font-size: 1.5rem;
            margin-right: 20px;
            color: var(--ca-accent-red);
            width: 28px;
            text-align: center;
            background: rgba(169, 4, 24, 0.1);
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .feature-item:hover i {
            background: var(--ca-accent-red);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
        }

        /* Right Side - Form */
        .login-form-section {
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: linear-gradient(135deg, var(--ca-white) 0%, #fafbfc 100%);
        }

        .form-container {
            max-width: 360px;
            width: 100%;
            margin: 0 auto;
        }

        .form-header {
            text-align: center;
            margin-bottom: 35px;
            animation: fadeInUp 0.6s ease;
        }

        .form-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--ca-dark-gray);
            margin: 0 0 12px 0;
            letter-spacing: -0.3px;
        }

        .form-subtitle {
            color: var(--ca-medium-gray);
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
        }

        /* Floating Label Inputs */
        .floating-label-group {
            position: relative;
            margin-bottom: 28px;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }

        .floating-input {
            width: 100%;
            padding: 22px 0 12px 0;
            border: none;
            border-bottom: 2px solid #e8ecef;
            background: transparent;
            font-size: 1.05rem;
            color: var(--ca-dark-gray);
            outline: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            position: relative;
            height: calc(1.6em + 1.25rem + 4px);
        }

        .floating-input:focus {
            border-bottom-color: var(--ca-focus-blue);
            background: transparent !important;
            transform: translateY(-1px);
        }

        .floating-input:hover:not(:focus) {
            border-bottom-color: var(--ca-focus-blue-border);
        }

        .floating-input:focus::placeholder {
            opacity: 0;
        }

        .floating-label {
            position: absolute;
            top: 22px;
            left: 0;
            font-size: 1.05rem;
            color: var(--ca-medium-gray);
            pointer-events: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            transform-origin: left top;
        }

        .floating-input:focus + .floating-label,
        .floating-input:valid + .floating-label,
        .floating-input:not(:placeholder-shown) + .floating-label {
            top: -12px;
            font-size: 0.85rem;
            color: var(--ca-focus-blue);
            font-weight: 700;
            transform: scale(0.9);
            text-shadow: 0 1px 2px rgba(1, 26, 45, 0.1);
        }

        .floating-input:hover:not(:focus) + .floating-label {
            color: var(--ca-focus-blue-border);
        }

        .input-border {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(135deg, var(--ca-focus-blue) 0%, var(--ca-secondary-blue) 100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(1, 26, 45, 0.3);
        }

        .floating-input:focus ~ .input-border {
            width: 100%;
        }

        .floating-input:hover:not(:focus) ~ .input-border {
            width: 30%;
            background: linear-gradient(135deg, var(--ca-focus-blue-border) 0%, rgba(26, 52, 97, 0.5) 100%);
        }

        /* Login Button */
        .login-btn {
            width: 100%;
            padding: 18px 35px;
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, var(--ca-accent-red-dark) 100%);
            border: none;
            border-radius: 60px;
            color: var(--ca-white);
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 35px;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 25px rgba(169, 4, 24, 0.3);
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.6s;
            animation-fill-mode: both;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, var(--ca-accent-red-dark) 0%, var(--ca-accent-red) 100%);
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(169, 4, 24, 0.4);
        }

        .login-btn:focus {
            outline: 3px solid var(--ca-focus-blue-border);
            outline-offset: 3px;
        }

        .login-btn:active {
            transform: translateY(-1px);
        }

        .btn-icon {
            transition: transform 0.3s ease;
            font-size: 1.2rem;
        }

        .login-btn:hover .btn-icon {
            transform: translateX(8px);
        }

        /* Alerts */
        .alert {
            padding: 18px 24px;
            border-radius: 12px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
            font-size: 0.95rem;
            animation: slideInDown 0.5s ease;
        }

        .alert-danger {
            background: rgba(169, 4, 24, 0.1);
            color: var(--ca-accent-red-dark);
            border: 1px solid rgba(169, 4, 24, 0.2);
            border-left: 4px solid var(--ca-accent-red);
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            border: 1px solid rgba(40, 167, 69, 0.2);
            border-left: 4px solid var(--ca-success-green);
        }

        /* Form Footer */
        .form-footer {
            text-align: center;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.8s;
            animation-fill-mode: both;
        }

        .back-link, .register-link {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 8px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border: 2px solid;
            text-align: center;
            min-width: 140px;
        }

        .back-link {
            background: white;
            color: var(--ca-focus-blue);
            border-color: var(--ca-focus-blue);
        }

        .back-link:hover {
            background: var(--ca-focus-blue);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
        }

        .register-link {
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, var(--ca-accent-red-dark) 100%);
            color: white;
            border-color: var(--ca-accent-red);
        }

        .register-link:hover {
            background: linear-gradient(135deg, var(--ca-accent-red-dark) 0%, var(--ca-accent-red) 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
        }

        /* Simple text link for registration */
        .register-text-link {
            color: var(--ca-medium-gray);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            margin-top: 15px;
            display: block;
        }

        .register-text-link:hover {
            color: var(--ca-focus-blue);
            text-decoration: underline;
        }



        /* OAuth Divider */
        .oauth-divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.5s;
            animation-fill-mode: both;
        }

        .oauth-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }

        .oauth-divider span {
            background: var(--ca-white);
            padding: 0 15px;
            color: var(--ca-medium-gray);
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Google OAuth Button */
        .google-btn {
            width: 100%;
            padding: 15px 20px;
            font-size: 1rem;
            font-weight: 500;
            color: var(--ca-dark-gray);
            background: var(--ca-white);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 25px;
            text-decoration: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.7s;
            animation-fill-mode: both;
        }

        .google-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            border-color: #dadce0;
            color: var(--ca-dark-gray);
            text-decoration: none;
        }

        .google-btn:active {
            transform: translateY(0);
        }

        .google-icon {
            flex-shrink: 0;
        }

        .logo-section {
            text-align: center;
        }

        .brand-logo-round {
            width: 300px;
            height: 300px;
            margin-bottom: 20px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .login-wrapper {
                grid-template-columns: 1fr;
                max-width: 450px;
            }

            .login-branding {
                padding: 40px 30px;
                min-height: 300px;
            }

            .brand-title {
                font-size: 2.2rem;
            }

            .features-list {
                margin-top: 30px;
            }

            .feature-item {
                font-size: 1rem;
                margin-bottom: 20px;
            }

            .login-form-section {
                padding: 40px 30px;
            }

            .form-title {
                font-size: 1.8rem;
            }

            .floating-input {
                font-size: 1rem;
            }

            .login-btn {
                padding: 18px 30px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 15px;
            }

            .login-branding {
                padding: 30px 20px;
            }

            .login-form-section {
                padding: 30px 20px;
            }

            .brand-title {
                font-size: 1.8rem;
            }

            .floating-input {
                font-size: 1rem;
            }

            .login-btn {
                padding: 18px 30px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class=\"login-container\">
        <div class=\"login-wrapper\">
            <!-- Left Side - Branding -->
            <div class=\"login-branding\">
                <div class=\"branding-content\">
                    <div class=\"logo-section\">
                        <img src=\"{{ asset('images/logos/logo-round.png') }}\" alt=\"Capitol Academy\" class=\"brand-logo-round\">
                        <h1 class=\"brand-title\">Capitol Academy</h1>
                    </div>
                </div>
            </div>

            <!-- Right Side - User Login Form -->
            <div class=\"login-form-section\">
                <div class=\"form-container\">
                    <!-- Form Header -->
                    <div class=\"form-header\">
                        <h2 class=\"form-title\">Welcome Back</h2>
                        <p class=\"form-subtitle\">Sign in to your account</p>
                    </div>

                    <!-- Error Messages -->
                    {% if error %}
                        <div class=\"alert alert-danger\">
                            <i class=\"fas fa-exclamation-triangle\"></i>
                            <span>{{ error.messageKey|trans(error.messageData, 'security') }}</span>
                        </div>
                    {% endif %}

                    <!-- Login Form -->
                    <form method=\"post\" action=\"{{ path('app_login') }}\">
                        <!-- Email Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"email\"
                                   class=\"floating-input\"
                                   name=\"_username\"
                                   value=\"{{ last_username }}\"
                                   placeholder=\" \"
                                   required
                                   autofocus>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-envelope\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Email Address
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Password Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"password\"
                                   class=\"floating-input\"
                                   name=\"_password\"
                                   placeholder=\" \"
                                   required>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>



                        <!-- CSRF Token -->
                        <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('authenticate') }}\">

                        <!-- Login Button -->
                        <button type=\"submit\" class=\"login-btn\">
                            <i class=\"fas fa-sign-in-alt btn-icon\"></i>
                            <span>Sign In</span>
                        </button>
                    </form>

                    <!-- Forgot Password Link -->
                    <div style=\"text-align: center; margin: 20px 0;\">
                        <a href=\"{{ path('app_forgot_password') }}\" style=\"color: var(--ca-focus-blue); text-decoration: none; font-weight: 600; font-size: 0.95rem; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.color='var(--ca-accent-red)'; this.style.textDecoration='underline'\"
                           onmouseout=\"this.style.color='var(--ca-focus-blue)'; this.style.textDecoration='none'\">
                            <i class=\"fas fa-key\" style=\"margin-right: 6px;\"></i>Forgot your password?
                        </a>
                    </div>

                    {# Google OAuth button - only show if properly configured #}
                    {% set google_client_id = app.request.server.get('GOOGLE_OAUTH_CLIENT_ID') %}
                    {% if google_client_id and google_client_id != 'your_google_oauth_client_id_here' %}
                    <!-- OAuth Divider -->
                    <div class=\"oauth-divider\">
                        <span>or</span>
                    </div>

                    <!-- Google OAuth Button -->
                    <a href=\"{{ path('connect_google_start') }}\" class=\"google-btn\">
                        <svg class=\"google-icon\" viewBox=\"0 0 24 24\" width=\"20\" height=\"20\">
                            <path fill=\"#4285F4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>
                            <path fill=\"#34A853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>
                            <path fill=\"#FBBC05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>
                            <path fill=\"#EA4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>
                        </svg>
                        <span>Continue with Google</span>
                    </a>
                    {% endif %}

                    <!-- Form Footer -->
                    <div class=\"form-footer\">
                        <a href=\"{{ path('app_home') }}\" class=\"back-link\">
                            <i class=\"fas fa-arrow-left me-2\"></i>Back to Website
                        </a>
                        <div style=\"margin-top: 15px;\">
                            <a href=\"{{ path('app_register') }}\" style=\"color: var(--ca-focus-blue); text-decoration: none; font-weight: 600; transition: all 0.3s ease;\"
                               onmouseover=\"this.style.color='var(--ca-accent-red)'; this.style.textDecoration='underline'\"
                               onmouseout=\"this.style.color='var(--ca-focus-blue)'; this.style.textDecoration='none'\">
                                Don't have an account? Register here
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => alert.remove(), 300);
            });
        }, 5000);

        // Enhanced form interactions
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('.floating-input');

            inputs.forEach(input => {
                // Handle autofill detection
                const checkAutofill = () => {
                    if (input.value !== '') {
                        input.classList.add('has-value');
                    } else {
                        input.classList.remove('has-value');
                    }
                };

                input.addEventListener('input', checkAutofill);
                input.addEventListener('blur', checkAutofill);

                // Initial check
                setTimeout(checkAutofill, 100);
            });
        });
    </script>
</body>
</html>", "security/login.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\security\\login.html.twig");
    }
}
