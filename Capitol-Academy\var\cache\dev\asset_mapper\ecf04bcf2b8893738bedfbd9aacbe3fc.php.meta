a:10:{i:0;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:78:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\assets\controllers.json";}i:1;O:51:"Symfony\Component\Config\Resource\DirectoryResource":2:{s:61:" Symfony\Component\Config\Resource\DirectoryResource resource";s:73:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\assets\controllers";s:60:" Symfony\Component\Config\Resource\DirectoryResource pattern";N;}i:2;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:112:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\symfony\stimulus-bundle\assets\dist\controllers.js";}i:3;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:110:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\symfony\ux-turbo\assets\dist\turbo_controller.js";}i:4;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:105:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\assets\vendor\@hotwired\stimulus\stimulus.index.js";s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:1;}i:5;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:99:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\assets\vendor\@hotwired\turbo\turbo.index.js";s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:1;}i:6;O:46:"Symfony\Component\Config\Resource\FileResource":1:{s:56:" Symfony\Component\Config\Resource\FileResource resource";s:93:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\assets\controllers\hello_controller.js";}i:7;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:105:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\assets\vendor\@hotwired\stimulus\stimulus.index.js";s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:1;}i:8;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:110:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\symfony\ux-turbo\assets\dist\turbo_controller.js";s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:1;}i:9;O:55:"Symfony\Component\Config\Resource\FileExistenceResource":2:{s:65:" Symfony\Component\Config\Resource\FileExistenceResource resource";s:93:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\assets\controllers\hello_controller.js";s:63:" Symfony\Component\Config\Resource\FileExistenceResource exists";b:1;}}