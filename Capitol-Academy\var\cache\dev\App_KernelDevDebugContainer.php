<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerCgnS2ob\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerCgnS2ob/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerCgnS2ob.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerCgnS2ob\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerCgnS2ob\App_KernelDevDebugContainer([
    'container.build_hash' => 'CgnS2ob',
    'container.build_id' => '79491ec4',
    'container.build_time' => 1752414953,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerCgnS2ob');
