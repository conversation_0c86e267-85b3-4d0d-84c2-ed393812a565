<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\Container7CdhYJX\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/Container7CdhYJX/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/Container7CdhYJX.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\Container7CdhYJX\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \Container7CdhYJX\App_KernelDevDebugContainer([
    'container.build_hash' => '7CdhYJX',
    'container.build_id' => 'e5dcba32',
    'container.build_time' => 1752412470,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'Container7CdhYJX');
