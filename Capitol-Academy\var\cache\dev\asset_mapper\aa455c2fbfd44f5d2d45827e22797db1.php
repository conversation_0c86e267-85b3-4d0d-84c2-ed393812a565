O:41:"Symfony\Component\AssetMapper\MappedAsset":12:{s:10:"sourcePath";s:74:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\assets\bootstrap.js";s:10:"publicPath";s:53:"/assets/bootstrap-c423b8bbc1f9cae218c105ca8ca9f767.js";s:23:"publicPathWithoutDigest";s:20:"/assets/bootstrap.js";s:15:"publicExtension";s:2:"js";s:7:"content";N;s:6:"digest";s:32:"c423b8bbc1f9cae218c105ca8ca9f767";s:13:"isPredigested";b:0;s:8:"isVendor";b:0;s:55:" Symfony\Component\AssetMapper\MappedAsset dependencies";a:0:{}s:59:" Symfony\Component\AssetMapper\MappedAsset fileDependencies";a:0:{}s:60:" Symfony\Component\AssetMapper\MappedAsset javaScriptImports";a:1:{i:0;O:56:"Symfony\Component\AssetMapper\ImportMap\JavaScriptImport":5:{s:10:"importName";s:24:"@symfony/stimulus-bundle";s:16:"assetLogicalPath";s:34:"@symfony/stimulus-bundle/loader.js";s:15:"assetSourcePath";s:107:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\symfony\stimulus-bundle\assets\dist\loader.js";s:6:"isLazy";b:0;s:24:"addImplicitlyToImportMap";b:0;}}s:11:"logicalPath";s:12:"bootstrap.js";}