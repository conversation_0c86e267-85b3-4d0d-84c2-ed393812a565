<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* components/admin_preview_layout.html.twig */
class __TwigTemplate_97ac82abf8aa4e72d0ebc8b0fe45ac55 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'preview_content' => [$this, 'block_preview_content'],
            'print_content' => [$this, 'block_print_content'],
            'print_body' => [$this, 'block_print_body'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 25
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "components/admin_preview_layout.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "components/admin_preview_layout.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 25);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 27
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["entity_title"]) || array_key_exists("entity_title", $context) ? $context["entity_title"] : (function () { throw new RuntimeError('Variable "entity_title" does not exist.', 27, $this->source); })()), "html", null, true);
        yield " - ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["entity_name"]) || array_key_exists("entity_name", $context) ? $context["entity_name"] : (function () { throw new RuntimeError('Variable "entity_name" does not exist.', 27, $this->source); })()), "html", null, true);
        yield " Details - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 29
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["entity_name"]) || array_key_exists("entity_name", $context) ? $context["entity_name"] : (function () { throw new RuntimeError('Variable "entity_name" does not exist.', 29, $this->source); })()), "html", null, true);
        yield " Details";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 31
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 32
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["breadcrumb_items"]) || array_key_exists("breadcrumb_items", $context) ? $context["breadcrumb_items"] : (function () { throw new RuntimeError('Variable "breadcrumb_items" does not exist.', 32, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["item"]) {
            // line 33
            yield "    ";
            if ((CoreExtension::getAttribute($this->env, $this->source, $context["item"], "active", [], "any", true, true, false, 33) && CoreExtension::getAttribute($this->env, $this->source, $context["item"], "active", [], "any", false, false, false, 33))) {
                // line 34
                yield "        <li class=\"breadcrumb-item active\">";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "title", [], "any", false, false, false, 34), "html", null, true);
                yield "</li>
    ";
            } else {
                // line 36
                yield "        <li class=\"breadcrumb-item\"><a href=\"";
                yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "path", [], "any", false, false, false, 36));
                yield "\">";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "title", [], "any", false, false, false, 36), "html", null, true);
                yield "</a></li>
    ";
            }
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['item'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 41
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 42
        yield "<div class=\"container-fluid\">
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"";
        // line 49
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("entity_icon", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["entity_icon"]) || array_key_exists("entity_icon", $context) ? $context["entity_icon"] : (function () { throw new RuntimeError('Variable "entity_icon" does not exist.', 49, $this->source); })()), "fas fa-file-alt")) : ("fas fa-file-alt")), "html", null, true);
        yield " mr-3\" style=\"font-size: 2rem;\"></i>
                        ";
        // line 50
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["entity_name"]) || array_key_exists("entity_name", $context) ? $context["entity_name"] : (function () { throw new RuntimeError('Variable "entity_name" does not exist.', 50, $this->source); })()), "html", null, true);
        yield " Details: ";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("entity_code", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["entity_code"]) || array_key_exists("entity_code", $context) ? $context["entity_code"] : (function () { throw new RuntimeError('Variable "entity_code" does not exist.', 50, $this->source); })()), (isset($context["entity_title"]) || array_key_exists("entity_title", $context) ? $context["entity_title"] : (function () { throw new RuntimeError('Variable "entity_title" does not exist.', 50, $this->source); })()))) : ((isset($context["entity_title"]) || array_key_exists("entity_title", $context) ? $context["entity_title"] : (function () { throw new RuntimeError('Variable "entity_title" does not exist.', 50, $this->source); })()))), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Button (Icon Only) -->
                        ";
        // line 56
        if (array_key_exists("edit_path", $context)) {
            // line 57
            yield "                        <a href=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["edit_path"]) || array_key_exists("edit_path", $context) ? $context["edit_path"] : (function () { throw new RuntimeError('Variable "edit_path" does not exist.', 57, $this->source); })()), "html", null, true);
            yield "\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease; text-decoration: none;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit ";
            // line 62
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["entity_name"]) || array_key_exists("entity_name", $context) ? $context["entity_name"] : (function () { throw new RuntimeError('Variable "entity_name" does not exist.', 62, $this->source); })()), "html", null, true);
            yield "\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>
                        ";
        }
        // line 66
        yield "
                        <!-- Print Button (Icon Only) -->
                        <button type=\"button\"
                                class=\"btn me-2 mb-2 mb-md-0\"
                                style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                                onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                                onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                                onclick=\"";
        // line 73
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((array_key_exists("print_function", $context)) ? (Twig\Extension\CoreExtension::default((isset($context["print_function"]) || array_key_exists("print_function", $context) ? $context["print_function"] : (function () { throw new RuntimeError('Variable "print_function" does not exist.', 73, $this->source); })()), "printDetails")) : ("printDetails")), "html", null, true);
        yield "()\"
                                title=\"Print ";
        // line 74
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["entity_name"]) || array_key_exists("entity_name", $context) ? $context["entity_name"] : (function () { throw new RuntimeError('Variable "entity_name" does not exist.', 74, $this->source); })()), "html", null, true);
        yield " Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </button>

                        <!-- Back Button -->
                        ";
        // line 79
        if (array_key_exists("back_path", $context)) {
            // line 80
            yield "                        <a href=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["back_path"]) || array_key_exists("back_path", $context) ? $context["back_path"] : (function () { throw new RuntimeError('Variable "back_path" does not exist.', 80, $this->source); })()), "html", null, true);
            yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease; text-decoration: none;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to ";
            // line 86
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["entity_name"]) || array_key_exists("entity_name", $context) ? $context["entity_name"] : (function () { throw new RuntimeError('Variable "entity_name" does not exist.', 86, $this->source); })()), "html", null, true);
            yield "s
                        </a>
                        ";
        }
        // line 89
        yield "                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body p-4\" style=\"background: white; border-radius: 0 0 15px 15px;\">
            ";
        // line 95
        yield from $this->unwrap()->yieldBlock('preview_content', $context, $blocks);
        // line 98
        yield "        </div>
    </div>
</div>

<!-- Professional Print Document (Hidden on Screen) -->
<div class=\"print-document\">
    ";
        // line 104
        yield from $this->unwrap()->yieldBlock('print_content', $context, $blocks);
        // line 124
        yield "</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 95
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_preview_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        // line 96
        yield "                <!-- Entity-specific content will be inserted here -->
            ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 104
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_print_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "print_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "print_content"));

        // line 105
        yield "        <!-- Print Header -->
        <div class=\"print-header\">
            <div class=\"print-logo\">CAPITOL ACADEMY</div>
            <div class=\"print-subtitle\">";
        // line 108
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["entity_name"]) || array_key_exists("entity_name", $context) ? $context["entity_name"] : (function () { throw new RuntimeError('Variable "entity_name" does not exist.', 108, $this->source); })()), "html", null, true);
        yield " Details Report</div>
            <div class=\"print-date\">Generated on ";
        // line 109
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate("now", "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "</div>
            <div class=\"print-confidential\">Confidential Document - For Internal Use Only</div>
        </div>

        ";
        // line 113
        yield from $this->unwrap()->yieldBlock('print_body', $context, $blocks);
        // line 116
        yield "
        <!-- Print Footer -->
        <div class=\"print-footer\">
            <div class=\"print-footer-line\">Capitol Academy - ";
        // line 119
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["entity_name"]) || array_key_exists("entity_name", $context) ? $context["entity_name"] : (function () { throw new RuntimeError('Variable "entity_name" does not exist.', 119, $this->source); })()), "html", null, true);
        yield " Management System</div>
            <div class=\"print-footer-line\">This document contains confidential information and is intended for authorized personnel only.</div>
            <div class=\"print-page-number\"></div>
        </div>
    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 113
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_print_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "print_body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "print_body"));

        // line 114
        yield "            <!-- Entity-specific print content will be inserted here -->
        ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 127
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 128
        yield "<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    font-weight: 600;
    color: #011a2d;
}

.enhanced-display-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

/* Professional Button Styling */
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* Standard Admin Button Styling */
.admin-btn-create {
    transition: all 0.3s ease !important;
}

.admin-btn-create:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(1, 26, 45, 0.2) !important;
}

/* Card Header Animation */
.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Professional Print Styles */
@media print {
    /* Reset and base styles */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    body {
        margin: 0;
        padding: 0;
        font-family: 'Times New Roman', serif;
        font-size: 12pt;
        line-height: 1.6;
        color: #000;
        background: white;
    }

    /* Hide all web interface elements */
    .navbar, .sidebar, .breadcrumb, .btn, .modal, .alert, .dropdown,
    .card-header, .quick-actions, .technical-info, .back-button,
    nav, header, footer, .container-fluid, .card, .row, .col-md-8, .col-md-4 {
        display: none !important;
    }

    /* Show only print content */
    .print-document {
        display: block !important;
        width: 100%;
        max-width: none;
        margin: 0;
        padding: 0;
    }

    /* Professional header with Capitol Academy branding */
    .print-header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 3px solid #1e3c72;
        page-break-inside: avoid;
    }

    .print-logo {
        font-size: 28pt;
        font-weight: bold;
        color: #1e3c72;
        margin-bottom: 8px;
        letter-spacing: 2px;
    }

    .print-subtitle {
        font-size: 16pt;
        color: #2a5298;
        margin-bottom: 12px;
        font-style: italic;
    }

    .print-date {
        font-size: 11pt;
        color: #666;
        margin-bottom: 5px;
    }

    .print-confidential {
        font-size: 10pt;
        color: #dc3545;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    /* Information sections */
    .print-section {
        margin-bottom: 30px;
        page-break-inside: avoid;
    }

    .print-section-title {
        font-size: 16pt;
        font-weight: bold;
        color: #1e3c72;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #2a5298;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .print-info-grid {
        display: table;
        width: 100%;
        border-collapse: collapse;
    }

    .print-info-row {
        display: table-row;
        border-bottom: 1px solid #eee;
    }

    .print-info-label {
        display: table-cell;
        font-weight: bold;
        color: #333;
        width: 180px;
        padding: 12px 15px 12px 0;
        vertical-align: top;
    }

    .print-info-value {
        display: table-cell;
        color: #000;
        padding: 12px 0;
        vertical-align: top;
    }

    /* Footer */
    .print-footer {
        position: fixed;
        bottom: 20px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 9pt;
        color: #666;
        border-top: 1px solid #ccc;
        padding-top: 15px;
        background: white;
    }

    .print-footer-line {
        margin-bottom: 5px;
    }

    /* Page numbering */
    .print-page-number:after {
        content: \"Page \" counter(page) \" of \" counter(pages);
    }

    /* Enhanced Page numbering and margins */
    @page {
        margin: 1in 0.75in;
        @top-center {
            content: \"Capitol Academy - Details Report\";
            font-size: 8pt;
            color: #7f8c8d;
            font-family: 'Georgia', serif;
        }
        @bottom-center {
            content: \"Page \" counter(page);
            font-size: 8pt;
            color: #7f8c8d;
            font-family: 'Georgia', serif;
        }
    }
}

/* Screen styles for print preview */
.print-document {
    display: none;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 351
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 352
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
<script>
function printDetails() {
    window.print();
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "components/admin_preview_layout.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  654 => 352,  641 => 351,  409 => 128,  396 => 127,  384 => 114,  371 => 113,  355 => 119,  350 => 116,  348 => 113,  341 => 109,  337 => 108,  332 => 105,  319 => 104,  307 => 96,  294 => 95,  282 => 124,  280 => 104,  272 => 98,  270 => 95,  262 => 89,  256 => 86,  246 => 80,  244 => 79,  236 => 74,  232 => 73,  223 => 66,  216 => 62,  207 => 57,  205 => 56,  194 => 50,  190 => 49,  181 => 42,  168 => 41,  147 => 36,  141 => 34,  138 => 33,  134 => 32,  121 => 31,  97 => 29,  71 => 27,  48 => 25,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{# 
    Standardized Admin Preview Layout Component
    Based on Course Preview Template Design
    
    Usage:
    {% embed 'components/admin_preview_layout.html.twig' with {
        'entity_name': 'User',
        'entity_title': user.fullName,
        'entity_code': user.id,
        'breadcrumb_items': [
            {'path': 'admin_dashboard', 'title': 'Home'},
            {'path': 'admin_users', 'title': 'Users'},
            {'title': user.fullName, 'active': true}
        ],
        'edit_path': path('admin_user_edit', {'id': user.id}),
        'back_path': path('admin_users'),
        'print_function': 'printUserDetails'
    } %}
        {% block preview_content %}
            <!-- Entity-specific content goes here -->
        {% endblock %}
    {% endembed %}
#}

{% extends 'admin/base.html.twig' %}

{% block title %}{{ entity_title }} - {{ entity_name }} Details - Capitol Academy Admin{% endblock %}

{% block page_title %}{{ entity_name }} Details{% endblock %}

{% block breadcrumbs %}
{% for item in breadcrumb_items %}
    {% if item.active is defined and item.active %}
        <li class=\"breadcrumb-item active\">{{ item.title }}</li>
    {% else %}
        <li class=\"breadcrumb-item\"><a href=\"{{ path(item.path) }}\">{{ item.title }}</a></li>
    {% endif %}
{% endfor %}
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"{{ entity_icon|default('fas fa-file-alt') }} mr-3\" style=\"font-size: 2rem;\"></i>
                        {{ entity_name }} Details: {{ entity_code|default(entity_title) }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Button (Icon Only) -->
                        {% if edit_path is defined %}
                        <a href=\"{{ edit_path }}\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease; text-decoration: none;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit {{ entity_name }}\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>
                        {% endif %}

                        <!-- Print Button (Icon Only) -->
                        <button type=\"button\"
                                class=\"btn me-2 mb-2 mb-md-0\"
                                style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                                onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                                onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                                onclick=\"{{ print_function|default('printDetails') }}()\"
                                title=\"Print {{ entity_name }} Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </button>

                        <!-- Back Button -->
                        {% if back_path is defined %}
                        <a href=\"{{ back_path }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease; text-decoration: none;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to {{ entity_name }}s
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body p-4\" style=\"background: white; border-radius: 0 0 15px 15px;\">
            {% block preview_content %}
                <!-- Entity-specific content will be inserted here -->
            {% endblock %}
        </div>
    </div>
</div>

<!-- Professional Print Document (Hidden on Screen) -->
<div class=\"print-document\">
    {% block print_content %}
        <!-- Print Header -->
        <div class=\"print-header\">
            <div class=\"print-logo\">CAPITOL ACADEMY</div>
            <div class=\"print-subtitle\">{{ entity_name }} Details Report</div>
            <div class=\"print-date\">Generated on {{ 'now'|date('F j, Y \\\\a\\\\t g:i A') }}</div>
            <div class=\"print-confidential\">Confidential Document - For Internal Use Only</div>
        </div>

        {% block print_body %}
            <!-- Entity-specific print content will be inserted here -->
        {% endblock %}

        <!-- Print Footer -->
        <div class=\"print-footer\">
            <div class=\"print-footer-line\">Capitol Academy - {{ entity_name }} Management System</div>
            <div class=\"print-footer-line\">This document contains confidential information and is intended for authorized personnel only.</div>
            <div class=\"print-page-number\"></div>
        </div>
    {% endblock %}
</div>
{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    font-weight: 600;
    color: #011a2d;
}

.enhanced-display-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

/* Professional Button Styling */
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* Standard Admin Button Styling */
.admin-btn-create {
    transition: all 0.3s ease !important;
}

.admin-btn-create:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(1, 26, 45, 0.2) !important;
}

/* Card Header Animation */
.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Professional Print Styles */
@media print {
    /* Reset and base styles */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    body {
        margin: 0;
        padding: 0;
        font-family: 'Times New Roman', serif;
        font-size: 12pt;
        line-height: 1.6;
        color: #000;
        background: white;
    }

    /* Hide all web interface elements */
    .navbar, .sidebar, .breadcrumb, .btn, .modal, .alert, .dropdown,
    .card-header, .quick-actions, .technical-info, .back-button,
    nav, header, footer, .container-fluid, .card, .row, .col-md-8, .col-md-4 {
        display: none !important;
    }

    /* Show only print content */
    .print-document {
        display: block !important;
        width: 100%;
        max-width: none;
        margin: 0;
        padding: 0;
    }

    /* Professional header with Capitol Academy branding */
    .print-header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 3px solid #1e3c72;
        page-break-inside: avoid;
    }

    .print-logo {
        font-size: 28pt;
        font-weight: bold;
        color: #1e3c72;
        margin-bottom: 8px;
        letter-spacing: 2px;
    }

    .print-subtitle {
        font-size: 16pt;
        color: #2a5298;
        margin-bottom: 12px;
        font-style: italic;
    }

    .print-date {
        font-size: 11pt;
        color: #666;
        margin-bottom: 5px;
    }

    .print-confidential {
        font-size: 10pt;
        color: #dc3545;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    /* Information sections */
    .print-section {
        margin-bottom: 30px;
        page-break-inside: avoid;
    }

    .print-section-title {
        font-size: 16pt;
        font-weight: bold;
        color: #1e3c72;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #2a5298;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .print-info-grid {
        display: table;
        width: 100%;
        border-collapse: collapse;
    }

    .print-info-row {
        display: table-row;
        border-bottom: 1px solid #eee;
    }

    .print-info-label {
        display: table-cell;
        font-weight: bold;
        color: #333;
        width: 180px;
        padding: 12px 15px 12px 0;
        vertical-align: top;
    }

    .print-info-value {
        display: table-cell;
        color: #000;
        padding: 12px 0;
        vertical-align: top;
    }

    /* Footer */
    .print-footer {
        position: fixed;
        bottom: 20px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 9pt;
        color: #666;
        border-top: 1px solid #ccc;
        padding-top: 15px;
        background: white;
    }

    .print-footer-line {
        margin-bottom: 5px;
    }

    /* Page numbering */
    .print-page-number:after {
        content: \"Page \" counter(page) \" of \" counter(pages);
    }

    /* Enhanced Page numbering and margins */
    @page {
        margin: 1in 0.75in;
        @top-center {
            content: \"Capitol Academy - Details Report\";
            font-size: 8pt;
            color: #7f8c8d;
            font-family: 'Georgia', serif;
        }
        @bottom-center {
            content: \"Page \" counter(page);
            font-size: 8pt;
            color: #7f8c8d;
            font-family: 'Georgia', serif;
        }
    }
}

/* Screen styles for print preview */
.print-document {
    display: none;
}
</style>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
function printDetails() {
    window.print();
}
</script>
{% endblock %}
", "components/admin_preview_layout.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\components\\admin_preview_layout.html.twig");
    }
}
