<?php

namespace ContainerCgnS2ob;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getAdminPermissionServiceService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Service\AdminPermissionService' shared autowired service.
     *
     * @return \App\Service\AdminPermissionService
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Service'.\DIRECTORY_SEPARATOR.'AdminPermissionService.php';

        return $container->privates['App\\Service\\AdminPermissionService'] = new \App\Service\AdminPermissionService(($container->privates['security.helper'] ?? self::getSecurity_HelperService($container)));
    }
}
